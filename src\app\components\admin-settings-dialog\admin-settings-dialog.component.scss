.admin-settings-dialog {
  min-width: 500px;
  max-width: 600px;

  .dialog-header {
    padding: 24px 24px 16px 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);

    h2 {
      margin: 0;
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 1.4rem;
      font-weight: 600;
      color: #1976d2;

      .header-icon {
        font-size: 1.6rem;
        width: 1.6rem;
        height: 1.6rem;
      }
    }
  }

  .dialog-content {
    padding: 24px;
    max-height: 70vh;
    overflow-y: auto;

    .settings-section {
      margin-bottom: 24px;

      .section-header {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 12px;

        .section-icon {
          color: #1976d2;
          font-size: 1.4rem;
          width: 1.4rem;
          height: 1.4rem;
        }

        h3 {
          margin: 0;
          font-size: 1.1rem;
          font-weight: 600;
          color: #333;
        }
      }

      .section-description {
        margin-bottom: 20px;
        padding-left: 44px;

        p {
          margin: 0;
          color: #666;
          font-size: 0.9rem;
          line-height: 1.5;
        }
      }

      .store-id-field {
        width: 100%;
        margin-bottom: 16px;

        ::ng-deep {
          .mat-mdc-form-field-wrapper {
            padding-bottom: 0;
          }

          .mat-mdc-text-field-wrapper {
            border-radius: 8px;
          }

          .mat-mdc-form-field-focus-overlay {
            border-radius: 8px;
          }

          .mat-mdc-form-field-hint-wrapper {
            padding-top: 8px;
          }
        }
      }

      .field-actions {
        display: flex;
        gap: 12px;
        justify-content: flex-end;

        .clear-button,
        .reset-button {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 0.875rem;
          color: #666;

          &:hover:not(:disabled) {
            background-color: rgba(0, 0, 0, 0.04);
          }

          &:disabled {
            color: #ccc;
          }

          mat-icon {
            font-size: 1.1rem;
            width: 1.1rem;
            height: 1.1rem;
          }
        }
      }
    }

    .info-section {
      background-color: #e3f2fd;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 16px;
      border-left: 4px solid #2196f3;

      .info-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;

        .info-icon {
          color: #1976d2;
          font-size: 1.2rem;
          width: 1.2rem;
          height: 1.2rem;
        }

        span {
          font-weight: 600;
          color: #1565c0;
          font-size: 0.95rem;
        }
      }

      .current-config {
        .config-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 0;

          .config-label {
            color: #1565c0;
            font-weight: 500;
            font-size: 0.9rem;
          }

          .config-value {
            color: #0d47a1;
            font-weight: 600;
            font-size: 0.9rem;
            background-color: rgba(255, 255, 255, 0.7);
            padding: 4px 8px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
          }
        }
      }
    }

    .warning-section {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 16px;
      background-color: #fff3e0;
      border-radius: 8px;
      border-left: 4px solid #ff9800;

      .warning-icon {
        color: #f57c00;
        font-size: 1.2rem;
        width: 1.2rem;
        height: 1.2rem;
      }

      span {
        color: #ef6c00;
        font-weight: 500;
        font-size: 0.875rem;
      }
    }
  }

  .dialog-actions {
    padding: 16px 24px 24px 24px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    border-top: 1px solid rgba(0, 0, 0, 0.12);
    background-color: #fafafa;

    .cancel-button {
      color: #666;
      font-weight: 500;

      &:hover:not(:disabled) {
        background-color: rgba(0, 0, 0, 0.04);
      }
    }

    .save-button {
      background-color: #1976d2;
      color: white;
      font-weight: 600;
      min-width: 100px;
      display: flex;
      align-items: center;
      gap: 8px;

      &:disabled {
        background-color: #ccc;
        color: #999;
      }

      &:not(:disabled):hover {
        background-color: #1565c0;
      }

      .loading-icon {
        animation: spin 1s linear infinite;
      }

      mat-icon {
        font-size: 1.1rem;
        width: 1.1rem;
        height: 1.1rem;
      }
    }
  }
}

// 加载动画
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 响应式设计
@media (max-width: 600px) {
  .admin-settings-dialog {
    min-width: 300px;
    max-width: 95vw;

    .dialog-header,
    .dialog-content,
    .dialog-actions {
      padding-left: 16px;
      padding-right: 16px;
    }

    .dialog-header h2 {
      font-size: 1.2rem;
    }

    .dialog-content {
      .settings-section {
        .section-description {
          padding-left: 0;
        }

        .field-actions {
          flex-direction: column;
          align-items: stretch;

          .clear-button,
          .reset-button {
            justify-content: center;
          }
        }
      }

      .info-section {
        .current-config {
          .config-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;
          }
        }
      }
    }

    .dialog-actions {
      flex-direction: column;
      gap: 8px;

      .cancel-button,
      .save-button {
        width: 100%;
        justify-content: center;
      }
    }
  }
}

// 全局snackbar样式
::ng-deep {
  .success-snackbar {
    background-color: #4caf50 !important;
    color: white !important;
  }

  .error-snackbar {
    background-color: #f44336 !important;
    color: white !important;
  }
}
