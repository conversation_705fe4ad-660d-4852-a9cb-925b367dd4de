.checkout-confirmation-dialog {
  width: 100%;
  max-width: 600px;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  overflow: hidden; // 确保整个弹窗不会溢出
}

// 弹窗标题
.dialog-header {
  text-align: center;
  padding: 24px 24px 16px;
  border-bottom: 1px solid #e5e7eb;

  .header-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    color: #3f51b5;
    margin-bottom: 16px;
  }

  h2 {
    margin: 0 0 8px 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
  }

  .header-subtitle {
    margin: 0;
    color: #6b7280;
    font-size: 0.875rem;
  }
}

// 弹窗内容
.dialog-content {
  padding: 0;
  overflow-y: auto;
  overflow-x: hidden;
  flex: 1;
  min-height: 0; // 允许flex子元素收缩
  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
    &:hover {
      background: #a8a8a8;
    }
  }
}

// 章节标题
.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 16px 0;
  padding: 16px 24px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;

  mat-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
    color: #3f51b5;
  }
}

// 商品列表
.items-section {
  .items-list {
    padding: 0 24px;
  }
}

.item-card {
  display: flex;
  gap: 16px;
  padding: 16px 0;
  border-bottom: 1px solid #f3f4f6;

  &:last-child {
    border-bottom: none;
  }

  .item-image {
    flex-shrink: 0;
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    background: #f9fafb;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .item-info {
    flex: 1;
    min-width: 0;

    .item-name {
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 4px;
      font-size: 0.875rem;
    }

    .item-description {
      font-size: 0.75rem;
      color: #6b7280;
      margin-bottom: 8px;
      line-height: 1.3;
    }

    .item-specs {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 0.75rem;
      color: #6b7280;
      margin-bottom: 8px;

      .spec-icon {
        font-size: 14px;
        width: 14px;
        height: 14px;
      }
    }

    .attribute-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;

      .attribute-tag {
        background: #e0f2fe;
        color: #0369a1;
        padding: 2px 6px;
        border-radius: 10px;
        font-size: 0.625rem;
        font-weight: 500;
      }
    }
  }

  .item-pricing {
    flex-shrink: 0;
    text-align: right;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 4px;

    .quantity-info {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 2px;
      font-size: 0.75rem;
      color: #6b7280;

      mat-icon {
        font-size: 12px;
        width: 12px;
        height: 12px;
      }

      .quantity {
        font-weight: 600;
      }
    }

    .price {
      font-size: 0.875rem;
      color: #6b7280;
    }

    .total-price {
      font-weight: 600;
      color: #1f2937;
      font-size: 1rem;
    }
  }
}

// 汇总部分
.summary-section {
  .summary-details {
    padding: 0 24px 16px;
  }
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;

  .label {
    font-size: 0.875rem;
    color: #6b7280;
  }

  .value {
    font-weight: 600;
    color: #1f2937;
  }

  &.total-row {
    .label {
      font-size: 1rem;
      font-weight: 600;
      color: #1f2937;
    }

    .total-amount {
      font-size: 1.25rem;
      color: #3f51b5;
    }
  }
}

.summary-divider {
  margin: 8px 0;
}

// 操作按钮
.dialog-actions {
  padding: 16px 24px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  gap: 12px;
  justify-content: flex-end;

  .cancel-btn {
    color: #6b7280;

    mat-icon {
      margin-right: 4px;
    }
  }

  .confirm-btn {
    min-width: 140px;

    mat-icon, mat-spinner {
      margin-right: 8px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .checkout-confirmation-dialog {
    max-width: 95vw;
    max-height: 95vh; // 在移动端使用更多的屏幕高度
    margin: 8px; // 减少边距以获得更多空间
  }

  .dialog-header {
    padding: 16px;

    .header-icon {
      font-size: 40px;
      width: 40px;
      height: 40px;
    }

    h2 {
      font-size: 1.25rem;
    }
  }

  .section-title {
    padding: 12px 16px 0;
    font-size: 1rem;
  }

  .items-section .items-list {
    padding: 0 16px;
  }

  .summary-section .summary-details {
    padding: 0 16px 12px;
  }

  .dialog-actions {
    padding: 12px 16px;
    flex-direction: column;

    .cancel-btn,
    .confirm-btn {
      width: 100%;
    }
  }

  .item-card {
    gap: 12px;

    .item-image {
      width: 50px;
      height: 50px;
    }

    .item-info {
      .item-name {
        font-size: 0.8rem;
      }

      .item-description {
        font-size: 0.7rem;
      }
    }

    .item-pricing {
      .total-price {
        font-size: 0.875rem;
      }
    }
  }
}

// 针对极小屏幕的额外优化
@media (max-height: 600px) {
  .checkout-confirmation-dialog {
    max-height: 98vh;
  }

  .dialog-header {
    padding: 12px 16px 8px;

    .header-icon {
      font-size: 36px;
      width: 36px;
      height: 36px;
      margin-bottom: 8px;
    }

    h2 {
      font-size: 1.1rem;
      margin-bottom: 4px;
    }

    .header-subtitle {
      font-size: 0.8rem;
    }
  }

  .section-title {
    padding: 8px 16px 0;
    font-size: 0.95rem;
    margin-bottom: 8px;
  }

  .dialog-actions {
    padding: 8px 16px;
  }
}
