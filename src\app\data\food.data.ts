export interface Food {
  id: string;  // 添加ID字段以匹配API数据
  name: string;
  description: string;
  price: number;
  image: string;
  categoryId: string;  // 添加分类ID字段
  images?: FoodImage[];  // 添加附图数组
  pluId?: string;  // 添加PLU ID字段
  barcode?: string;  // 添加条形码字段
}

// 商品附图接口
export interface FoodImage {
  id: string;
  imagePath: string;
  sortOrder: number;
}

export interface Category {
  id: string;  // 添加分类ID
  name: string;
  route: string;
  imagePath?: string;  // 添加分类图片路径
}

// API返回的分类数据结构
export interface ApiCategory {
  id: string;
  name: string;
  stortOrder: number;
  parentId: string | null;
  createDate: string;
}

// API返回的食物数据结构
export interface ApiFoodItem {
  id: string;
  foodCategory_ID: string;
  productDetails: string;
  foodImagePath: string;
  plU_ID: string;
  barcode: string;
  createDate: string;
  description: string;
  price: number;
  priceBase: number;
  sortOrder: number;
}

