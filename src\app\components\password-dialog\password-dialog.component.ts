import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormControl, Validators } from '@angular/forms';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { StorageService } from '../../service/storage.service';

export interface PasswordDialogData {
  title?: string;
  message?: string;
}

@Component({
  selector: 'app-password-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule
  ],
  templateUrl: './password-dialog.component.html',
  styleUrls: ['./password-dialog.component.scss']
})
export class PasswordDialogComponent {
  passwordControl = new FormControl('', [Validators.required]);
  hidePassword = true;
  errorMessage = '';

  constructor(
    public dialogRef: MatDialogRef<PasswordDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: PasswordDialogData,
    private storageService: StorageService
  ) {}

  /**
   * 切换密码显示/隐藏
   */
  togglePasswordVisibility(): void {
    this.hidePassword = !this.hidePassword;
  }

  /**
   * 验证密码
   */
  validatePassword(): void {
    this.errorMessage = '';
    
    if (this.passwordControl.invalid) {
      this.errorMessage = '请输入密码';
      return;
    }

    const password = this.passwordControl.value || '';
    
    if (this.storageService.validateDatePassword(password)) {
      // 密码正确，关闭对话框并返回成功结果
      this.dialogRef.close(true);
    } else {
      // 密码错误
      this.errorMessage = `密码错误。请输入今天的日期 (格式: ${this.storageService.getCurrentDatePassword()})`;
      this.passwordControl.setValue('');
    }
  }

  /**
   * 取消操作
   */
  onCancel(): void {
    this.dialogRef.close(false);
  }

  /**
   * 处理回车键
   */
  onEnterKey(): void {
    if (!this.passwordControl.invalid) {
      this.validatePassword();
    }
  }
}
