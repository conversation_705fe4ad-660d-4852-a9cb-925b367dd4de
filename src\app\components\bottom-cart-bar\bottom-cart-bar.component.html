<!-- 底部购物车栏 - 仅在有商品时显示 -->
<div *ngIf="shouldShowBar()" class="bottom-cart-container">
  <!-- 展开的商品列表 -->
  <div class="cart-items-panel" [class.expanded]="isPanelOpen">
    <div class="items-list" *ngIf="isPanelOpen && !isCartEmpty()">
      <div *ngFor="let item of cartState?.items" class="cart-item">
        <div class="item-content" (click)="editItem(item)">
          <div class="item-image">
            <img [src]="item.image || '/assets/images/placeholder-food.svg'"
                 [alt]="item.name"
                 onerror="this.src='/assets/images/placeholder-food.svg'">
          </div>

          <div class="item-info">
            <div class="item-name">{{ item.name }}</div>
            <div *ngIf="item.selectedAttributes && item.selectedAttributes.length > 0"
                 class="item-specs">
              {{ getSelectedSpecs(item.selectedAttributes) }}
            </div>
            <div class="item-price">${{ item.price.toFixed(2) }}</div>
          </div>
        </div>

        <div class="item-controls" (click)="$event.stopPropagation()">
          <div class="quantity-controls">
            <button (click)="decreaseQuantity(item)"
                    class="quantity-btn">
              −
            </button>
            <span class="quantity">{{ item.quantity }}</span>
            <button (click)="increaseQuantity(item)"
                    class="quantity-btn">
              +
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 底部购物车栏 -->
  <div class="bottom-cart-bar">
    <!-- 同步进度条 -->
    <mat-progress-bar
      *ngIf="isSyncing()"
      mode="indeterminate"
      color="primary"
      class="sync-progress">
    </mat-progress-bar>

    <!-- 错误信息显示 -->
    <div *ngIf="hasError()" class="error-banner">
      <span>{{ getErrorMessage() }}</span>
      <button (click)="clearError()" class="error-close-btn">×</button>
    </div>

    <!-- 主要内容区域 -->
    <div class="cart-bar-content">
      <!-- 左侧：购物车信息 -->
      <div class="cart-info" (click)="toggleCartPanel()">
        <div class="cart-summary">
          <div class="item-count">
            <mat-icon class="cart-icon" [class.syncing]="isSyncing()">shopping_cart</mat-icon>
            <span class="count-text">{{ getItemCount() }} items</span>
          </div>
          <div class="total-price">
            <div class="amount">${{ getTotalAmount().toFixed(2) }}</div>
            <div class="tax-amount">Tax: ${{ getTotalTaxAmount().toFixed(2) }}</div>
          </div>
        </div>

        <!-- 展开/收起箭头 -->
        <div class="expand-arrow">
          <mat-icon [class.rotated]="isPanelOpen">keyboard_arrow_up</mat-icon>
        </div>
      </div>

      <!-- 右侧：结算按钮 -->
      <div class="checkout-section">
        <button
          mat-raised-button
          color="primary"
          class="checkout-btn"
          [disabled]="isSyncing()"
          (click)="proceedToCheckout()">
          <span *ngIf="!isSyncing()">Checkout</span>
          <span *ngIf="isSyncing()">Syncing...</span>
        </button>
      </div>
    </div>
  </div>
</div>
