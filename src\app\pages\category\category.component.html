<div class="category-container">
  <div class="food-sections">
    <div *ngFor="let category of categories; let i = index; trackBy: trackByCategories"
         [id]="category.route.substring(1)"
         class="category-section"
         (click)="onCategorySelect(i)">

      <!-- 分类标题 -->
      <div class="category-header">
        <h2 class="category-title">{{ category.name }}</h2>
      </div>

      <div class="food-grid">
        <!-- 显示loading状态 -->
        <div *ngIf="isLoadingFoodsForCategory(category.id)" class="loading-message">
          Loading menu items...
        </div>
        <!-- 显示食物卡片 -->
        <mat-card *ngFor="let food of getFoodsByCategory(category.route); trackBy: trackByFoods"
                  class="food-card">
          <!-- 商品图片 -->
          <div class="food-image-container" (click)="onFoodClick(food)">
            <img [src]="food.image"
                 [alt]="food.name"
                 [style.visibility]="isImageLoaded(food.image) ? 'visible' : 'hidden'"
                 (load)="onImageLoad(food.image)"
                 (error)="onImageError($event, food.image)"
                 loading="lazy"
                 fetchpriority="low"
                 decoding="async">
          </div>

          <mat-card-content class="clickable-content"
                           (click)="onFoodClick(food)">
            <h3>{{ food.name }}</h3>
            <p class="price">${{ food.price.toFixed(2) }}</p>

            <!-- 描述区域 - 始终显示以保持布局一致 -->
            <p class="description">
              {{ food.description && food.description.trim() ? food.description : '' }}
            </p>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </div>
</div>