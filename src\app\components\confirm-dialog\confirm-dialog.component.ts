import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { CommonModule } from '@angular/common';

export interface ConfirmDialogData {
  title: string;
  message: string;
  confirmText: string;
  cancelText: string;
}

@Component({
  selector: 'app-confirm-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule
  ],
  template: `
    <div class="confirm-dialog">
      <div class="dialog-header">
        <div class="icon-container">
          <svg class="warning-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <h2 class="dialog-title">{{ data.title }}</h2>
      </div>

      <div class="dialog-content">
        <p class="dialog-message">{{ data.message }}</p>
      </div>

      <div class="dialog-actions">
        <button type="button" (click)="onCancel()" class="cancel-btn">
          {{ data.cancelText }}
        </button>
        <button type="button" (click)="onConfirm()" class="confirm-btn">
          {{ data.confirmText }}
        </button>
      </div>
    </div>
  `,
  styles: [`
    :host {
      --primary-color: #2563eb;
      --error-color: #ef4444;
      --error-hover: #dc2626;
      --gray-50: #f8fafc;
      --gray-100: #f1f5f9;
      --gray-200: #e2e8f0;
      --gray-400: #94a3b8;
      --gray-500: #64748b;
      --gray-600: #475569;
      --gray-700: #334155;
      --gray-800: #1e293b;
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
      --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .confirm-dialog {
      min-width: 380px;
      max-width: 420px;
      background: linear-gradient(145deg, #ffffff 0%, var(--gray-50) 100%);
      border-radius: 16px;
      overflow: hidden;
      position: relative;
      box-shadow: var(--shadow-xl);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .confirm-dialog::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background:
        radial-gradient(circle at 20% 80%, rgba(239, 68, 68, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(37, 99, 235, 0.03) 0%, transparent 50%);
      pointer-events: none;
      z-index: 0;
    }

    .dialog-header {
      position: relative;
      z-index: 1;
      padding: 24px 24px 16px 24px;
      text-align: center;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
      backdrop-filter: blur(10px);
      border-bottom: 1px solid var(--gray-200);
    }

    .icon-container {
      display: flex;
      justify-content: center;
      margin-bottom: 16px;
    }

    .warning-icon {
      width: 48px;
      height: 48px;
      color: var(--error-color);
      background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
      border-radius: 50%;
      padding: 12px;
      box-shadow: 0 4px 12px rgba(239, 68, 68, 0.15);
      animation: iconPulse 2s ease-in-out infinite;
    }

    @keyframes iconPulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.05); }
    }

    .dialog-title {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--gray-800);
      line-height: 1.4;
    }

    .dialog-content {
      position: relative;
      z-index: 1;
      padding: 16px 24px 24px 24px;
      text-align: center;
    }

    .dialog-message {
      margin: 0;
      font-size: 0.95rem;
      color: var(--gray-600);
      line-height: 1.5;
    }

    .dialog-actions {
      position: relative;
      z-index: 1;
      display: flex;
      justify-content: center;
      gap: 12px;
      padding: 0 24px 24px 24px;
    }

    .cancel-btn, .confirm-btn {
      flex: 1;
      max-width: 140px;
      height: 44px;
      border: none;
      border-radius: 12px;
      font-size: 0.9rem;
      font-weight: 500;
      cursor: pointer;
      transition: var(--transition);
      position: relative;
      overflow: hidden;
    }

    .cancel-btn {
      background: linear-gradient(135deg, #ffffff 0%, var(--gray-100) 100%);
      color: var(--gray-600);
      border: 1px solid var(--gray-200);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .cancel-btn:hover {
      background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
      color: var(--gray-700);
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .cancel-btn:active {
      transform: translateY(0);
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }

    .confirm-btn {
      background: linear-gradient(135deg, var(--error-color) 0%, var(--error-hover) 100%);
      color: white;
      box-shadow: 0 4px 12px rgba(239, 68, 68, 0.25);
    }

    .confirm-btn:hover {
      background: linear-gradient(135deg, var(--error-hover) 0%, #b91c1c 100%);
      transform: translateY(-1px);
      box-shadow: 0 6px 16px rgba(239, 68, 68, 0.35);
    }

    .confirm-btn:active {
      transform: translateY(0);
      box-shadow: 0 2px 6px rgba(239, 68, 68, 0.3);
    }

    /* 响应式设计 */
    @media (max-width: 480px) {
      .confirm-dialog {
        min-width: 320px;
        max-width: 340px;
        margin: 16px;
      }

      .dialog-header {
        padding: 20px 20px 14px 20px;
      }

      .warning-icon {
        width: 40px;
        height: 40px;
        padding: 10px;
      }

      .dialog-title {
        font-size: 1.125rem;
      }

      .dialog-content {
        padding: 14px 20px 20px 20px;
      }

      .dialog-message {
        font-size: 0.9rem;
      }

      .dialog-actions {
        padding: 0 20px 20px 20px;
        gap: 10px;
      }

      .cancel-btn, .confirm-btn {
        height: 40px;
        font-size: 0.85rem;
      }
    }
  `]
})
export class ConfirmDialogComponent {
  constructor(
    public dialogRef: MatDialogRef<ConfirmDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmDialogData
  ) {}

  onCancel(): void {
    this.dialogRef.close(false);
  }

  onConfirm(): void {
    this.dialogRef.close(true);
  }
}
