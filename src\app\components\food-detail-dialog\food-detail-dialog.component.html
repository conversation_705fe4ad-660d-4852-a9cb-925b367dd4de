<div class="food-detail-dialog">
  <!-- 关闭按钮 -->
  <div class="dialog-header">
    <button mat-icon-button mat-dialog-close class="close-button">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <div class="dialog-content" mat-dialog-content>
    <!-- 左侧：图片、价格、详细信息 -->
    <div class="left-section">
      <!-- 图片区域 -->
      <div class="image-carousel-container">
        <div class="image-carousel"
             #imageContainer
             (touchstart)="onTouchStart($event)"
             (touchmove)="onTouchMove($event)"
             (touchend)="onTouchEnd($event)">

          <!-- 图片容器 -->
          <div class="image-container"
               [style.transform]="'translateX(' + (-currentImageIndex * 100) + '%)'">
            <div class="image-slide" *ngFor="let image of allImages; let i = index">
              <img [src]="image"
                   [alt]="data.food.name"
                   class="product-image"
                   (error)="onImageError($event)">
            </div>
          </div>

          <!-- 导航按钮 -->
          <div class="navigation-buttons" *ngIf="allImages.length > 1">
            <!-- 上一张按钮 -->
            <button class="nav-button nav-button-prev"
                    (click)="previousImage()"
                    [disabled]="isTransitioning">
              <mat-icon>chevron_left</mat-icon>
            </button>

            <!-- 下一张按钮 -->
            <button class="nav-button nav-button-next"
                    (click)="nextImage()"
                    [disabled]="isTransitioning">
              <mat-icon>chevron_right</mat-icon>
            </button>
          </div>

          <!-- 分页指示器 -->
          <div class="pagination-indicator" *ngIf="allImages.length > 1">
            {{ currentImageIndex + 1 }}/{{ allImages.length }}
          </div>
        </div>
      </div>

      <!-- 商品基本信息 -->
      <div class="product-header">
        <div class="price-display">
          <span class="current-price">${{ productConfig.totalPrice.toFixed(2) }}</span>
        </div>
      </div>

      <!-- 商品详情描述 -->
      <div class="product-details-section">
        <h3>{{ getProductName() }}</h3>
        <p class="product-description">{{ getProductDescription() }}</p>
      </div>

      <!-- 加载状态 -->
      <div *ngIf="isLoading" class="loading-section">
        <div class="loading-content">
          <mat-spinner diameter="40"></mat-spinner>
          <p>Loading options...</p>
        </div>
      </div>

      <!-- 错误状态 -->
      <div *ngIf="error && !isLoading" class="error-section">
        <mat-icon color="warn">error</mat-icon>
        <p>{{ error }}</p>
      </div>
    </div>

    <!-- 右侧：规格选择 -->
    <div class="right-section">
      <!-- 内容容器 - 使用绝对定位实现无缝切换 -->
      <div class="content-container">
        <!-- 骨架屏 -->
        <div class="skeleton-section" [class.visible]="isLoading">
          <!-- 动态骨架屏选项组 -->
          <div class="skeleton-option-group" *ngFor="let item of getSkeletonGroups()">
            <div class="skeleton-group-header">
              <div class="skeleton-title"></div>
              <div class="skeleton-badge"></div>
            </div>
            <div class="skeleton-select-field"></div>
            <!-- 为多选组预留选中标签空间 -->
            <div class="skeleton-selected-tags">
              <div class="skeleton-tag"></div>
              <div class="skeleton-tag"></div>
            </div>
          </div>


        </div>

        <!-- 规格选择和备注区域的父容器 -->
        <div class="options-and-memo-container">
          <!-- 规格选择区域 -->
          <div class="options-section" [class.visible]="itemDetails && itemDetails.attributeGroup && !isLoading">

          <!-- 遍历所有属性组，只显示minimumSelected > 0的组 -->
          <div *ngFor="let group of itemDetails?.attributeGroup" class="option-group">
          <div *ngIf="safeGetProperty(group, 'minimumSelected', 0) > 0">

          <!-- 必选项 (单选或多选但minimumSelected > 0) -->
          <div *ngIf="isGroupRequired(group)" class="required-group">
            <div class="group-header">
              <h3>{{ getGroupTitle(group) }}</h3>
              <span class="required-badge" *ngIf="!isGroupSelected(group)">Required</span>
            </div>

            <!-- 单选组 - 使用下拉框 -->
            <div *ngIf="isGroupSingleSelection(group)" class="select-group">
              <mat-form-field appearance="outline" class="full-width">
                <mat-select
                  [value]="getSelectedRadioValue(safeGetProperty(group, 'id', ''))"
                  (selectionChange)="onSelectChange(safeGetProperty(group, 'id', ''), $event)"
                  placeholder="Choose one option"
                  [ngClass]="getSelectCssClass(safeGetProperty(group, 'id', ''))">

                  <!-- 自定义触发器 -->
                  <mat-select-trigger>
                    <div class="custom-trigger">
                      <span class="trigger-text">
                        {{ getTriggerDisplayText(safeGetProperty(group, 'id', '')) || 'Choose one option' }}
                      </span>
                      <mat-icon class="trigger-check-icon" *ngIf="hasGroupSelection(safeGetProperty(group, 'id', ''))">check</mat-icon>
                    </div>
                  </mat-select-trigger>

                  <mat-option
                    *ngFor="let item of safeGetProperty(group, 'items', [])"
                    [value]="safeGetProperty(item, 'id', '')">
                    <span class="option-name">{{ safeGetProperty(item, 'name', 'Option') }}</span>
                    <span class="option-price" *ngIf="safeGetProperty(item, 'price', 0) > 0">
                      (+${{ safeGetProperty(item, 'price', 0).toFixed(2) }})
                    </span>
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>

            <!-- 多选组 - 使用下拉框 -->
            <div *ngIf="isGroupMultipleSelection(group)" class="select-group">
              <mat-form-field appearance="outline" class="full-width">
                <mat-select
                  #multiSelect
                  [value]="getSelectedItemsForGroup(safeGetProperty(group, 'id', ''))"
                  (selectionChange)="onMultiSelectChange(safeGetProperty(group, 'id', ''), $event)"
                  placeholder="Choose options"
                  multiple
                  [attr.data-group-id]="safeGetProperty(group, 'id', '')"
                  [ngClass]="getSelectCssClass(safeGetProperty(group, 'id', ''))">

                  <!-- 自定义触发器 -->
                  <mat-select-trigger>
                    <div class="custom-trigger">
                      <span class="trigger-text">
                        {{ getTriggerDisplayText(safeGetProperty(group, 'id', '')) || 'Choose options' }}
                      </span>
                      <mat-icon class="trigger-check-icon" *ngIf="hasGroupSelection(safeGetProperty(group, 'id', ''))">check</mat-icon>
                    </div>
                  </mat-select-trigger>

                  <mat-option
                    *ngFor="let item of safeGetProperty(group, 'items', [])"
                    [value]="safeGetProperty(item, 'id', '')">
                    <span class="option-name">{{ safeGetProperty(item, 'name', 'Option') }}</span>
                    <span class="option-price" *ngIf="safeGetProperty(item, 'price', 0) > 0">
                      (+${{ safeGetProperty(item, 'price', 0).toFixed(2) }})
                    </span>
                  </mat-option>

                  <!-- Done按钮 - 仅多选显示 -->
                  <div class="select-done-button">
                    <button mat-button color="primary" (click)="closePanelForGroup(multiSelect)">
                      Done
                    </button>
                  </div>
                </mat-select>
              </mat-form-field>

              <!-- 显示选中的选项（仅多选组） -->
              <div *ngIf="getSelectedItemsForGroup(safeGetProperty(group, 'id', '')).length > 0" class="selected-items">
                <div *ngFor="let selectedItem of getSelectedItemsForGroup(safeGetProperty(group, 'id', ''))"
                     class="selected-item-tag">
                  <mat-icon class="check-icon">check</mat-icon>
                  <span class="item-name">{{ getItemName(group, selectedItem) }}</span>
                  <span class="item-price" *ngIf="getItemPrice(group, selectedItem) > 0">
                    +${{ getItemPrice(group, selectedItem).toFixed(2) }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 可选项 (minimumSelected === 0) -->
          <div *ngIf="!isGroupRequired(group)" class="optional-group">
            <div class="group-header">
              <h3>{{ getGroupTitle(group) }}</h3>
            </div>

            <!-- 单选组 - 使用下拉框 -->
            <div *ngIf="isGroupSingleSelection(group)" class="select-group">
              <mat-form-field appearance="outline" class="full-width">
                <mat-select
                  [value]="getSelectedRadioValue(safeGetProperty(group, 'id', ''))"
                  (selectionChange)="onSelectChange(safeGetProperty(group, 'id', ''), $event)"
                  placeholder="Choose one option"
                  [ngClass]="getSelectCssClass(safeGetProperty(group, 'id', ''))">

                  <!-- 自定义触发器 -->
                  <mat-select-trigger>
                    <div class="custom-trigger">
                      <span class="trigger-text">
                        {{ getTriggerDisplayText(safeGetProperty(group, 'id', '')) || 'Choose one option' }}
                      </span>
                      <mat-icon class="trigger-check-icon" *ngIf="hasGroupSelection(safeGetProperty(group, 'id', ''))">check</mat-icon>
                    </div>
                  </mat-select-trigger>

                  <mat-option value="">None</mat-option>
                  <mat-option
                    *ngFor="let item of safeGetProperty(group, 'items', [])"
                    [value]="safeGetProperty(item, 'id', '')">
                    <span class="option-name">{{ safeGetProperty(item, 'name', 'Option') }}</span>
                    <span class="option-price" *ngIf="safeGetProperty(item, 'price', 0) > 0">
                      (+${{ safeGetProperty(item, 'price', 0).toFixed(2) }})
                    </span>
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>

            <!-- 多选组 - 使用下拉框 -->
            <div *ngIf="isGroupMultipleSelection(group)" class="select-group">
              <mat-form-field appearance="outline" class="full-width">
                <mat-select
                  #multiSelectOptional
                  [value]="getSelectedItemsForGroup(safeGetProperty(group, 'id', ''))"
                  (selectionChange)="onMultiSelectChange(safeGetProperty(group, 'id', ''), $event)"
                  placeholder="Choose options"
                  multiple
                  [attr.data-group-id]="safeGetProperty(group, 'id', '')"
                  [ngClass]="getSelectCssClass(safeGetProperty(group, 'id', ''))">

                  <!-- 自定义触发器 -->
                  <mat-select-trigger>
                    <div class="custom-trigger">
                      <span class="trigger-text">
                        {{ getTriggerDisplayText(safeGetProperty(group, 'id', '')) || 'Choose options' }}
                      </span>
                      <mat-icon class="trigger-check-icon" *ngIf="hasGroupSelection(safeGetProperty(group, 'id', ''))">check</mat-icon>
                    </div>
                  </mat-select-trigger>

                  <mat-option
                    *ngFor="let item of safeGetProperty(group, 'items', [])"
                    [value]="safeGetProperty(item, 'id', '')">
                    <span class="option-name">{{ safeGetProperty(item, 'name', 'Option') }}</span>
                    <span class="option-price" *ngIf="safeGetProperty(item, 'price', 0) > 0">
                      (+${{ safeGetProperty(item, 'price', 0).toFixed(2) }})
                    </span>
                  </mat-option>

                  <!-- Done按钮 - 仅多选显示 -->
                  <div class="select-done-button">
                    <button mat-button color="primary" (click)="closePanelForGroup(multiSelectOptional)">
                      Done
                    </button>
                  </div>
                </mat-select>
              </mat-form-field>

              <!-- 显示选中的选项（仅多选组） -->
              <div *ngIf="getSelectedItemsForGroup(safeGetProperty(group, 'id', '')).length > 0" class="selected-items">
                <div *ngFor="let selectedItem of getSelectedItemsForGroup(safeGetProperty(group, 'id', ''))"
                     class="selected-item-tag">
                  <mat-icon class="check-icon">check</mat-icon>
                  <span class="item-name">{{ getItemName(group, selectedItem) }}</span>
                  <span class="item-price" *ngIf="getItemPrice(group, selectedItem) > 0">
                    +${{ getItemPrice(group, selectedItem).toFixed(2) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
          </div> <!-- 结束 minimumSelected > 0 条件 -->
        </div>



          <!-- Notes section - Clean design -->
          <div class="memo-section">
            <mat-form-field appearance="outline" class="memo-field">
              <mat-label>Notes</mat-label>
              <textarea matInput
                        [(ngModel)]="productConfig.memo"
                        rows="3"
                        maxlength="500"
                        class="memo-textarea"
                        #memoTextarea></textarea>
              <mat-hint align="end">{{ memoTextarea.value.length || 0 }}/500</mat-hint>
            </mat-form-field>
          </div>

          <!-- 验证错误 -->
          <div *ngIf="validationErrors.length > 0" class="validation-errors">
            <div *ngFor="let error of validationErrors" class="error-message">
              <mat-icon color="warn">warning</mat-icon>
              <span>{{ error }}</span>
            </div>
          </div>
          </div>
        </div> <!-- 结束 options-and-memo-container -->

        <!-- 错误状态 -->
        <div class="error-section" [class.visible]="error && !isLoading">
          <mat-icon color="warn">error</mat-icon>
          <p>{{ error }}</p>
        </div>
      </div>
    </div>
  </div>

  <!-- 错误信息显示 -->
  <div *ngIf="addToCartError" class="error-message">
    {{ addToCartError }}
  </div>

  <!-- 底部操作区域 -->
  <div class="dialog-actions" mat-dialog-actions>
    <div class="quantity-controls">
      <button class="quantity-btn" (click)="decreaseQuantity()" [disabled]="productConfig.quantity <= 1">
        −
      </button>
      <span class="quantity-display">{{ productConfig.quantity }}</span>
      <button class="quantity-btn" (click)="increaseQuantity()">
        +
      </button>
    </div>

    <button class="add-to-cart-btn"
            (click)="addToCart()"
            [disabled]="!canAddToCart()">
      <span *ngIf="!isAddingToCart">Add to Cart ${{ productConfig.totalPrice.toFixed(2) }}</span>
      <span *ngIf="isAddingToCart">Adding to Cart...</span>
    </button>
  </div>
</div>
