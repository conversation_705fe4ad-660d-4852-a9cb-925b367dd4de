<div class="admin-settings-dialog">
  <div class="dialog-header">
    <h2 mat-dialog-title>
      <mat-icon class="header-icon">settings</mat-icon>
      {{ data.title || '管理员设置' }}
    </h2>
  </div>

  <div mat-dialog-content class="dialog-content">
    <div class="settings-section">
      <div class="section-header">
        <mat-icon class="section-icon">store</mat-icon>
        <h3>店铺配置</h3>
      </div>
      
      <div class="section-description">
        <p>配置当前店铺的唯一标识符，用于订单管理和数据同步。</p>
      </div>

      <mat-form-field appearance="outline" class="store-id-field">
        <mat-label>Store ID</mat-label>
        <input matInput
               [formControl]="storeIdControl"
               placeholder="请输入店铺ID"
               autocomplete="off"
               maxlength="50">
        <mat-icon matSuffix>business</mat-icon>
        <mat-hint align="start">店铺的唯一标识符</mat-hint>
        <mat-hint align="end">{{ (storeIdControl.value || '').length }}/50</mat-hint>
        <mat-error *ngIf="storeIdControl.invalid && storeIdControl.touched">
          {{ getStoreIdErrorMessage() }}
        </mat-error>
      </mat-form-field>

      <div class="field-actions">
        <button mat-button 
                (click)="onClear()" 
                class="clear-button"
                [disabled]="isLoading">
          <mat-icon>clear</mat-icon>
          清空
        </button>
        <button mat-button 
                (click)="onReset()" 
                class="reset-button"
                [disabled]="isLoading || !hasUnsavedChanges()">
          <mat-icon>refresh</mat-icon>
          重置
        </button>
      </div>
    </div>

    <div class="info-section" *ngIf="originalStoreId">
      <div class="info-header">
        <mat-icon class="info-icon">info</mat-icon>
        <span>当前配置</span>
      </div>
      <div class="current-config">
        <div class="config-item">
          <span class="config-label">当前Store ID:</span>
          <span class="config-value">{{ originalStoreId }}</span>
        </div>
      </div>
    </div>

    <div class="warning-section" *ngIf="hasUnsavedChanges()">
      <mat-icon class="warning-icon">warning</mat-icon>
      <span>您有未保存的更改</span>
    </div>
  </div>

  <div mat-dialog-actions class="dialog-actions">
    <button mat-button 
            (click)="onCancel()" 
            class="cancel-button"
            [disabled]="isLoading">
      取消
    </button>
    <button mat-raised-button 
            color="primary" 
            (click)="onSave()"
            [disabled]="storeIdControl.invalid || isLoading || !hasUnsavedChanges()"
            class="save-button">
      <mat-icon *ngIf="isLoading" class="loading-icon">hourglass_empty</mat-icon>
      <mat-icon *ngIf="!isLoading">save</mat-icon>
      {{ isLoading ? '保存中...' : '保存' }}
    </button>
  </div>
</div>
