@import '@angular/material/prebuilt-themes/indigo-pink.css';

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-size: 16px;
  overflow: hidden;
}

* {
  box-sizing: inherit;
}

h1 { font-size: 2rem; }
h2 { font-size: 1.8rem; }
h3 { font-size: 1.5rem; }
p { font-size: 1rem; }

button {
  font-size: 1rem;
  padding: 8px 16px;
}

/* Dialog 样式修复 */
.food-detail-dialog-container .mat-mdc-dialog-container {
  padding: 0 !important;
  border-radius: 16px !important;
  overflow: hidden !important;
  background: white !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
}

/* 确认对话框样式 */
.mat-mdc-dialog-container:has(.confirm-dialog) {
  padding: 0 !important;
  border-radius: 16px !important;
  overflow: hidden !important;
  background: transparent !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
}

.mat-mdc-dialog-surface:has(.confirm-dialog) {
  border-radius: 16px !important;
  background: transparent !important;
}

.food-detail-dialog-container .mat-mdc-dialog-surface {
  border-radius: 16px !important;
  background: white !important;
}

/* 确保对话框内容样式正确应用 */
.food-detail-dialog-container .mat-mdc-dialog-content {
  margin: 0 !important;
  padding: 0 !important;
  max-height: none !important;
}

.food-detail-dialog-container .mat-mdc-dialog-actions {
  margin: 0 !important;
  padding: 0 !important;
  min-height: auto !important;
}

/* 确保 Material Icons 正确显示 */
.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}