import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatDialog } from '@angular/material/dialog';
import { PasswordDialogComponent } from '../../components/password-dialog/password-dialog.component';
import { AdminSettingsDialogComponent } from '../../components/admin-settings-dialog/admin-settings-dialog.component';
import { StorageService } from '../../service/storage.service';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [CommonModule, MatIconModule],
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.css']
})
export class HomeComponent {

  constructor(
    private router: Router,
    private dialog: MatDialog,
    private storageService: StorageService
  ) {}

  // 导航到Asian食物页面
  startOrdering(): void {
    this.router.navigate(['/asian']);
  }

  /**
   * 打开管理员设置
   */
  openAdminSettings(): void {
    // 首先打开密码验证对话框
    const passwordDialogRef = this.dialog.open(PasswordDialogComponent, {
      width: '450px',
      disableClose: true,
      data: {
        title: 'Administrator Verification',
        message: 'Please enter administrator password to access settings'
      }
    });

    passwordDialogRef.afterClosed().subscribe(result => {
      if (result === true) {
        // 密码验证通过，打开设置对话框
        this.openSettingsDialog();
      }
      // 如果result为false或undefined，表示取消或密码错误，不做任何操作
    });
  }

  /**
   * 打开设置对话框
   */
  private openSettingsDialog(): void {
    const settingsDialogRef = this.dialog.open(AdminSettingsDialogComponent, {
      width: '550px',
      disableClose: true,
      data: {
        title: '管理员设置'
      }
    });

    settingsDialogRef.afterClosed().subscribe(result => {
      if (result && result.saved) {
        console.log('设置已保存:', result.storeId);
        // 这里可以添加额外的逻辑，比如刷新某些数据
      }
    });
  }
}
