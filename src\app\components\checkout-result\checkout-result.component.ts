import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { Router } from '@angular/router';
import { TransactionOrderResponse, Product, AttributeItem } from '../../service/backoffice';

@Component({
  selector: 'app-checkout-result',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatDividerModule
  ],
  templateUrl: './checkout-result.component.html',
  styleUrls: ['./checkout-result.component.scss']
})
export class CheckoutResultComponent implements OnInit {
  orderResult: TransactionOrderResponse | null = null;
  loading = true;
  error: string | null = null;

  constructor(
    private router: Router
  ) {}

  ngOnInit(): void {
    console.log('📄 结算结果页面初始化');

    // 从路由状态中获取结算结果
    const navigation = this.router.getCurrentNavigation();
    console.log('🔍 检查路由导航状态:', navigation);
    console.log('导航extras:', navigation?.extras);
    console.log('导航state:', navigation?.extras?.state);

    if (navigation?.extras?.state?.['orderResult']) {
      this.orderResult = navigation.extras.state['orderResult'];
      console.log('✅ 成功从路由状态获取订单结果:', this.orderResult);
      console.log('订单ID:', this.orderResult?.transactionID);
      console.log('订单金额:', this.orderResult?.totalAmount);
      console.log('商品数量:', this.orderResult?.products?.length);
      this.loading = false;
    } else {
      console.error('❌ 路由状态中未找到订单信息');
      console.log('可能的原因:');
      console.log('1. 页面刷新导致状态丢失');
      console.log('2. 直接访问此页面');
      console.log('3. Angular路由状态传递失败');

      // 如果没有结算结果，重定向到首页
      this.error = 'Order information not found. You will be redirected to the homepage.';
      this.loading = false;
      setTimeout(() => {
        this.router.navigate(['/']);
      }, 3000);
    }
  }

  /**
   * 格式化交易时间
   */
  formatTransactionDate(): string {
    if (!this.orderResult?.transactionDate) return '';
    return new Date(this.orderResult.transactionDate).toLocaleString('zh-CN');
  }

  /**
   * 格式化价格
   */
  formatPrice(price: number | undefined): string {
    if (price === undefined || price === null) return '$0.00';
    return `$${price.toFixed(2)}`;
  }

  /**
   * 获取商品的属性显示文本
   */
  getAttributesText(product: Product): string {
    if (!product.attributesPre || product.attributesPre.length === 0) {
      return '无规格';
    }
    return product.attributesPre.join(', ');
  }

  /**
   * 获取商品的属性详情
   */
  getAttributesDetails(product: Product): AttributeItem[] {
    return product.attributes || [];
  }

  /**
   * 返回首页
   */
  goHome(): void {
    this.router.navigate(['/']);
  }

  /**
   * 继续购物
   */
  continueShopping(): void {
    this.router.navigate(['/asian']);
  }
}
