import {
  MAT_BUTTON_CONFIG,
  MAT_FAB_DEFAULT_OPTIONS,
  MAT_FAB_DEFAULT_OPTIONS_FACTORY,
  Mat<PERSON>nchor,
  MatButton,
  MatButtonModule,
  MatFabAnchor,
  MatFabButton,
  MatIconAnchor,
  Mat<PERSON><PERSON>Button,
  Mat<PERSON>iniFabAnchor,
  MatMiniFabButton
} from "./chunk-3JX55Q53.js";
import "./chunk-EIHLO3V2.js";
import "./chunk-T4BX4TUX.js";
import "./chunk-RQZSDY3S.js";
import "./chunk-UND7GNPC.js";
import "./chunk-IGF3QYBZ.js";
import "./chunk-6Q4RANH6.js";
import "./chunk-FFZIAYYX.js";
import "./chunk-CXCX2JKZ.js";
export {
  MAT_BUTTON_CONFIG,
  MAT_FAB_DEFAULT_OPTIONS,
  MAT_FAB_DEFAULT_OPTIONS_FACTORY,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Mat<PERSON>uttonModule,
  <PERSON><PERSON>abAnch<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Mat<PERSON>conAnchor,
  MatIconButton,
  MatMiniFabAnchor,
  MatMiniFabButton
};
//# sourceMappingURL=@angular_material_button.js.map
