import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { RouterModule } from '@angular/router';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatListModule } from '@angular/material/list';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { CommonModule } from '@angular/common';
import { Category } from '../../data/food.data';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { CategoryComponent } from '../category/category.component';
import { CategoryService } from '../../service/category.service';
import { BottomCartBarComponent } from '../../components/bottom-cart-bar/bottom-cart-bar.component';
import { UserActivityService } from '../../service/user-activity.service';
import { CartService } from '../../service/cart.service';
import { StorageService } from '../../service/storage.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-asian',
  templateUrl: './asian.component.html',
  styleUrls: ['./asian.component.css'],
  standalone: true,
  imports: [
    RouterModule,
    MatSidenavModule,
    MatListModule,
    MatToolbarModule,
    MatIconModule,
    MatButtonModule,
    MatDialogModule,
    CommonModule,
    CategoryComponent,
    BottomCartBarComponent
  ]
})
export class AsianComponent implements OnInit, OnDestroy {
  categories: Category[] = [];
  isMobile = false;
  currentCategoryIndex = 0;
  isLoading = true;
  error: string | null = null;

  // 用户活动监听相关
  private subscriptions: Subscription[] = [];

  // 分类图标映射
  private categoryIconMap: { [key: string]: string } = {
    'appetizer': 'restaurant',
    'soup': 'soup_kitchen',
    'main': 'dinner_dining',
    'rice': 'rice_bowl',
    'noodle': 'ramen_dining',
    'dessert': 'cake',
    'drink': 'local_cafe',
    'coffee': 'coffee',
    'tea': 'emoji_food_beverage',
    'snack': 'cookie',
    'seafood': 'set_meal',
    'meat': 'lunch_dining',
    'vegetable': 'eco',
    'salad': 'grass',
    'pizza': 'local_pizza',
    'burger': 'lunch_dining',
    'sandwich': 'lunch_dining',
    'default': 'restaurant_menu'
  };

  constructor(
    private breakpointObserver: BreakpointObserver,
    private categoryService: CategoryService,
    private userActivityService: UserActivityService,
    private cartService: CartService,
    private dialog: MatDialog,
    private storageService: StorageService
  ) {}

  ngOnInit() {
    // 监听屏幕尺寸变化
    this.breakpointObserver
      .observe([Breakpoints.Handset])
      .subscribe(result => {
        this.isMobile = result.matches;
      });

    // 加载分类数据
    this.loadCategories();

    // 启动用户活动监听
    this.initUserActivityMonitoring();
  }

  ngOnDestroy() {
    // 停止用户活动监听
    this.userActivityService.stopMonitoring();

    // 取消所有订阅
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * 初始化用户活动监听
   */
  private initUserActivityMonitoring() {
    // 启动监听，设置5分钟超时
    const timeoutMs = 5 * 60 * 1000; // 5分钟
    this.userActivityService.startMonitoring(timeoutMs, '/home');

    console.log('用户活动监听已启动，超时时间: 5分钟');
  }

  /**
   * 加载分类数据
   */
  loadCategories() {
    this.isLoading = true;
    this.error = null;

    // 从本地存储获取storeId
    const storeIdStr = this.storageService.getStoreId();
    if (!storeIdStr) {
      this.error = '未设置店铺ID，请联系管理员进行配置';
      this.isLoading = false;
      console.error('未找到storeId，请先在管理员设置中配置');
      return;
    }

    const storeId = parseInt(storeIdStr, 10);
    if (isNaN(storeId)) {
      this.error = '店铺ID格式错误，请联系管理员重新配置';
      this.isLoading = false;
      console.error('storeId格式错误:', storeIdStr);
      return;
    }

    console.log('使用storeId加载分类数据:', storeId);

    this.categoryService.getCategoriesForStore(storeId).subscribe({
      next: (categories) => {
        this.categories = categories;
        console.log("分类数据为:", categories);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('加载分类数据失败:', error);
        this.error = '加载分类数据失败，请稍后重试';
        this.isLoading = false;
      }
    });
  }

  scrollToCategory(categoryId: string) {
    const element = document.getElementById(categoryId);
    if (element) {
      // 使用更稳定的滚动方式，避免与其他滚动事件冲突
      const container = document.querySelector('.food-sections');
      if (container) {
        const elementTop = element.offsetTop;
        const containerTop = container.scrollTop;
        const targetScrollTop = elementTop - 20; // 留一些顶部间距

        // 使用requestAnimationFrame确保滚动平滑且不冲突
        this.smoothScrollTo(container, containerTop, targetScrollTop, 300);
      } else {
        // 备用方案
        element.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }
  }

  private smoothScrollTo(element: Element, start: number, end: number, duration: number) {
    const startTime = performance.now();

    const scroll = (currentTime: number) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用easeInOutCubic缓动函数
      const easeProgress = progress < 0.5
        ? 4 * progress * progress * progress
        : 1 - Math.pow(-2 * progress + 2, 3) / 2;

      const currentPosition = start + (end - start) * easeProgress;
      element.scrollTop = currentPosition;

      if (progress < 1) {
        requestAnimationFrame(scroll);
      }
    };

    requestAnimationFrame(scroll);
  }

  onCategoryChange(index: number) {
    this.currentCategoryIndex = index;
  }

  trackByCategories(index: number, category: Category): string {
    return category.route;
  }

  /**
   * 根据分类名称获取对应的Material Icon
   * @param categoryName 分类名称
   * @returns Material Icon名称
   */
  getCategoryIcon(categoryName: string): string {
    const name = categoryName.toLowerCase();

    // 检查是否包含关键词
    for (const [key, icon] of Object.entries(this.categoryIconMap)) {
      if (name.includes(key)) {
        return icon;
      }
    }

    // 返回默认图标
    return this.categoryIconMap['default'];
  }

  /**
   * 分类图片加载错误处理
   * @param event 错误事件
   * @param category 分类对象
   */
  onCategoryImageError(event: Event, category: Category): void {
    console.warn('分类图片加载失败:', category.imagePath);
    const imgElement = event.target as HTMLImageElement;
    if (imgElement) {
      // 隐藏图片，显示fallback图标
      imgElement.style.display = 'none';
      // 清除imagePath，让模板显示fallback图标
      category.imagePath = undefined;
    }
  }


}
