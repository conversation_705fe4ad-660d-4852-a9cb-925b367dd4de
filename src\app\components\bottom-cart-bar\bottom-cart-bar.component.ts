import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { CartService, CartState, CartItem } from '../../service/cart.service';
import { FoodDetailDialogComponent } from '../food-detail-dialog/food-detail-dialog.component';
import { Food } from '../../data/food.data';
import { ConfirmDialogComponent } from '../confirm-dialog/confirm-dialog.component';
import { CheckoutConfirmationDialogComponent } from '../checkout-confirmation-dialog/checkout-confirmation-dialog.component';
import { StorageService } from '../../service/storage.service';

@Component({
  selector: 'app-bottom-cart-bar',
  templateUrl: './bottom-cart-bar.component.html',
  styleUrls: ['./bottom-cart-bar.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatProgressBarModule
  ]
})
export class BottomCartBarComponent implements OnInit, OnDestroy {
  cartState: CartState | null = null;
  isPanelOpen = false;

  private cartStateSubscription?: Subscription;

  constructor(
    private cartService: CartService,
    private dialog: MatDialog,
    private router: Router,
    private storageService: StorageService
  ) {}

  ngOnInit(): void {
    // 订阅购物车状态变化
    this.cartStateSubscription = this.cartService.cartState$.subscribe(cartState => {
      console.log('底部购物车栏收到状态更新:', cartState);
      this.cartState = cartState;
    });
  }

  ngOnDestroy(): void {
    if (this.cartStateSubscription) {
      this.cartStateSubscription.unsubscribe();
    }
  }

  /**
   * 检查购物车是否为空
   */
  isCartEmpty(): boolean {
    return !this.cartState || this.cartState.items.length === 0;
  }

  /**
   * 获取购物车商品总数
   */
  getItemCount(): number {
    return this.cartState?.itemCount || 0;
  }

  /**
   * 获取购物车总价
   */
  getTotalAmount(): number {
    return this.cartState?.totalAmount || 0;
  }

  /**
   * 获取购物车税额
   */
  getTotalTaxAmount(): number {
    return this.cartState?.totalTaxAmount || 0;
  }

  /**
   * 检查是否正在同步
   */
  isSyncing(): boolean {
    return this.cartState?.loading === true;
  }

  /**
   * 切换购物车详情面板
   */
  toggleCartPanel(): void {
    console.log('切换购物车详情面板');
    this.isPanelOpen = !this.isPanelOpen;
  }

  /**
   * 前往结算 - 先显示确认弹窗
   */
  proceedToCheckout(): void {
    // 从本地存储获取storeId
    const storeIdStr = this.storageService.getStoreId();
    if (!storeIdStr) {
      console.error('未找到storeId，请先在管理员设置中配置');
      alert('Store ID not set, please contact administrator to configure');
      return;
    }

    const storeId = parseInt(storeIdStr, 10);
    if (isNaN(storeId)) {
      console.error('storeId格式错误:', storeIdStr);
      alert('Store ID format error, please contact administrator to reconfigure');
      return;
    }

    // 检查购物车状态
    if (!this.cartState || this.cartState.items.length === 0) {
      alert('Cart is empty, cannot proceed to checkout');
      return;
    }

    console.log('打开结算确认弹窗:', { storeId, cartState: this.cartState });

    // 打开结算确认弹窗
    const dialogRef = this.dialog.open(CheckoutConfirmationDialogComponent, {
      width: '600px',
      maxWidth: '95vw',
      maxHeight: '90vh',
      disableClose: true,
      data: {
        cartState: this.cartState,
        storeId: storeId
      }
    });

    // 处理弹窗结果
    dialogRef.afterClosed().subscribe(result => {
      if (result && result.action === 'confirm') {
        console.log('用户确认结算，开始调用API:', result);
        this.performCheckout(result.storeId);
      } else {
        console.log('用户取消结算');
      }
    });
  }

  /**
   * 执行实际的结算操作
   */
  private performCheckout(storeId: number): void {
    console.log('开始执行结算:', storeId);

    this.cartService.checkout(storeId).subscribe({
      next: (orderResult) => {
        console.log('结算成功，跳转到结果页面:', orderResult);
        // 导航到结算结果页面，并传递订单结果
        this.router.navigate(['/checkout-result'], {
          state: { orderResult }
        });
      },
      error: (error) => {
        console.error('结算失败:', error);
        // 这里可以显示错误提示
        alert('结算失败，请重试');
      }
    });
  }

  /**
   * 检查是否应该显示底部栏
   */
  shouldShowBar(): boolean {
    return !this.isCartEmpty();
  }

  /**
   * 增加商品数量
   */
  increaseQuantity(item: CartItem): void {
    console.log('增加商品数量:', item);

    this.cartService.increaseQuantity(item).subscribe({
      next: (response) => {
        console.log('=== 商品数量增加成功 ===');
        console.log('返回信息:', response);
      },
      error: (error) => {
        console.error('增加商品数量失败:', error);
      }
    });
  }

  /**
   * 减少商品数量或确认删除
   */
  decreaseQuantity(item: CartItem): void {
    console.log('减少商品数量:', item);

    if (item.quantity <= 1) {
      // 当数量为1时，弹出确认删除对话框
      this.confirmRemoveItem(item);
      return;
    }

    this.cartService.decreaseQuantity(item).subscribe({
      next: (response) => {
        console.log('=== 商品数量减少成功 ===');
        console.log('返回信息:', response);
      },
      error: (error) => {
        console.error('减少商品数量失败:', error);
      }
    });
  }

  /**
   * 确认删除商品对话框
   */
  private confirmRemoveItem(item: CartItem): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirm Removal',
        message: `Do you want to remove "${item.name}" from your cart?`,
        confirmText: 'Remove',
        cancelText: 'Cancel'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result === true) {
        this.removeItem(item);
      }
    });
  }

  /**
   * 删除商品
   */
  removeItem(item: CartItem): void {
    console.log('删除商品:', item);

    this.cartService.removeItem(item).subscribe({
      next: (response) => {
        console.log('=== 商品删除成功 ===');
        console.log('返回信息:', response);
      },
      error: (error) => {
        console.error('删除商品失败:', error);
      }
    });
  }

  /**
   * 检查是否有错误
   */
  hasError(): boolean {
    return this.cartState?.error !== null && this.cartState?.error !== undefined;
  }

  /**
   * 获取错误信息
   */
  getErrorMessage(): string {
    return this.cartState?.error || '';
  }

  /**
   * 清除错误信息
   */
  clearError(): void {
    this.cartService.clearError();
  }

  /**
   * 编辑购物车商品
   */
  editItem(item: CartItem): void {
    console.log('编辑商品:', item.name);

    // 构造Food对象用于打开商品详情对话框
    // 由于后端会处理价格计算，我们只需要提供商品基本信息
    const food: Food = {
      id: item.foodItemId,
      name: item.name,
      description: item.description || '',
      price: 0, // 价格由后端计算，这里设为0
      image: item.image || '',
      categoryId: '', // 编辑时不需要分类ID
    };

    const dialogRef = this.dialog.open(FoodDetailDialogComponent, {
      maxWidth: '1300px',
      width: '95vw',
      maxHeight: '117vh',
      panelClass: 'food-detail-dialog-container',
      hasBackdrop: true,
      disableClose: false,
      data: {
        food: food,
        storeId: 2,
        editMode: true, // 标识为编辑模式
        cartItem: item // 传入购物车商品信息
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && result.action === 'item-updated') {
        console.log('商品规格已更新:', result.productConfig);
        // 更新操作已在对话框中处理
      }
    });
  }

  /**
   * 获取选择的规格显示文本
   * 使用productConfig中的完整属性信息（包含名称）
   */
  getSelectedSpecs(selectedAttributes: any[]): string {
    if (!selectedAttributes || selectedAttributes.length === 0) {
      return '';
    }

    return selectedAttributes.map(attr => {
      if (attr.items && attr.items.length > 0) {
        return attr.items.map((item: any) => {
          // 显示名称，如果有价格则加上价格信息
          let display = item.name || '选项';
          if (item.price && item.price > 0) {
            display += ` (+$${item.price.toFixed(2)})`;
          }
          return display;
        }).join(', ');
      }
      return '';
    }).filter(spec => spec).join('; ');
  }
}
