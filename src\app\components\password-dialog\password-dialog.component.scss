.password-dialog {
  min-width: 400px;
  max-width: 500px;

  .dialog-header {
    padding: 24px 24px 16px 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);

    h2 {
      margin: 0;
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 1.25rem;
      font-weight: 600;
      color: #1976d2;

      .header-icon {
        font-size: 1.5rem;
        width: 1.5rem;
        height: 1.5rem;
      }
    }
  }

  .dialog-content {
    padding: 24px;

    .dialog-message {
      margin: 0 0 16px 0;
      color: #424242;
      font-size: 0.95rem;
      line-height: 1.5;
    }

    .password-hint {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 16px;
      background-color: #e3f2fd;
      border-radius: 8px;
      margin-bottom: 24px;
      border-left: 4px solid #2196f3;

      .hint-icon {
        color: #1976d2;
        font-size: 1.2rem;
        width: 1.2rem;
        height: 1.2rem;
      }

      span {
        color: #1565c0;
        font-size: 0.875rem;
        font-weight: 500;
      }
    }

    .password-field {
      width: 100%;
      margin-bottom: 16px;

      ::ng-deep {
        .mat-mdc-form-field-wrapper {
          padding-bottom: 0;
        }

        .mat-mdc-text-field-wrapper {
          border-radius: 8px;
        }

        .mat-mdc-form-field-focus-overlay {
          border-radius: 8px;
        }
      }
    }

    .error-message {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 16px;
      background-color: #ffebee;
      border-radius: 8px;
      border-left: 4px solid #f44336;
      animation: shake 0.5s ease-in-out;

      .error-icon {
        color: #d32f2f;
        font-size: 1.2rem;
        width: 1.2rem;
        height: 1.2rem;
      }

      span {
        color: #c62828;
        font-size: 0.875rem;
        font-weight: 500;
      }
    }
  }

  .dialog-actions {
    padding: 16px 24px 24px 24px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    border-top: 1px solid rgba(0, 0, 0, 0.12);

    .cancel-button {
      color: #666;
      font-weight: 500;

      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }
    }

    .confirm-button {
      background-color: #1976d2;
      color: white;
      font-weight: 600;
      min-width: 80px;

      &:disabled {
        background-color: #ccc;
        color: #999;
      }

      &:not(:disabled):hover {
        background-color: #1565c0;
      }
    }
  }
}

// 错误消息震动动画
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

// 响应式设计
@media (max-width: 480px) {
  .password-dialog {
    min-width: 300px;
    max-width: 90vw;

    .dialog-header,
    .dialog-content,
    .dialog-actions {
      padding-left: 16px;
      padding-right: 16px;
    }

    .dialog-header h2 {
      font-size: 1.1rem;
    }

    .password-hint span {
      font-size: 0.8rem;
    }
  }
}
