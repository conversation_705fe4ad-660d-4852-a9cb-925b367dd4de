import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatBadgeModule } from '@angular/material/badge';
import { Subscription } from 'rxjs';
import { CartService, CartState } from '../../service/cart.service';

@Component({
  selector: 'app-cart-fab',
  templateUrl: './cart-fab.component.html',
  styleUrls: ['./cart-fab.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatBadgeModule
  ]
})
export class CartFabComponent implements OnInit, OnDestroy {
  cartState: CartState | null = null;
  private cartStateSubscription?: Subscription;

  constructor(private cartService: CartService) {}

  ngOnInit(): void {
    // 订阅购物车状态变化
    this.cartStateSubscription = this.cartService.cartState$.subscribe(cartState => {
      console.log('购物车浮动按钮收到状态更新:', cartState);
      this.cartState = cartState;
    });
  }

  ngOnDestroy(): void {
    if (this.cartStateSubscription) {
      this.cartStateSubscription.unsubscribe();
    }
  }

  /**
   * 获取购物车商品数量
   */
  getItemCount(): number {
    return this.cartState?.itemCount || 0;
  }

  /**
   * 获取购物车总价
   */
  getTotalAmount(): number {
    return this.cartState?.totalAmount || 0;
  }

  /**
   * 检查是否有商品
   */
  hasItems(): boolean {
    return this.getItemCount() > 0;
  }

  /**
   * 检查是否正在加载
   */
  isSyncing(): boolean {
    return this.cartState?.loading === true;
  }

  /**
   * 检查是否显示徽章
   */
  shouldShowBadge(): boolean {
    return this.hasItems();
  }

  /**
   * 点击购物车按钮
   */
  onCartClick(): void {
    console.log('TODO: 购物车FAB按钮被点击');
    // TODO: 实现切换购物车侧边栏逻辑
  }
}
