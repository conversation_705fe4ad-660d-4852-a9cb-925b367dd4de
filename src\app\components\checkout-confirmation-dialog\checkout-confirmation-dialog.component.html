<div class="checkout-confirmation-dialog">
  <!-- 弹窗标题 -->
  <div mat-dialog-title class="dialog-header">
    <mat-icon class="header-icon">shopping_cart</mat-icon>
    <h2>Confirm Order</h2>
    <p class="header-subtitle">Please confirm your order details</p>
  </div>

  <!-- 弹窗内容 -->
  <div mat-dialog-content class="dialog-content">
    <!-- 商品列表 -->
    <div class="items-section">
      <h3 class="section-title">
        <mat-icon>restaurant_menu</mat-icon>
        Order Items ({{ getTotalQuantity() }} items)
      </h3>
      
      <div class="items-list">
        <div *ngFor="let item of getCartItems(); trackBy: trackByItemId" class="item-card">
          <!-- 商品图片 -->
          <div class="item-image">
            <img [src]="getItemImageUrl(item)" 
                 [alt]="item.name"
                 (error)="onImageError($event)"
                 loading="lazy">
          </div>

          <!-- 商品信息 -->
          <div class="item-info">
            <div class="item-name">{{ item.name }}</div>
            <div class="item-description" *ngIf="item.description">
              {{ item.description }}
            </div>
            
            <!-- 商品规格 -->
            <div class="item-specs" *ngIf="getAttributesText(item) !== 'No specifications'">
              <mat-icon class="spec-icon">tune</mat-icon>
              <span>{{ getAttributesText(item) }}</span>
            </div>

            <!-- 属性标签 -->
            <div class="attribute-tags" *ngIf="getAttributeTags(item).length > 0">
              <span *ngFor="let tag of getAttributeTags(item)" class="attribute-tag">
                {{ tag.name }} (+{{ formatPrice(tag.price) }})
              </span>
            </div>
          </div>

          <!-- 数量和价格 -->
          <div class="item-pricing">
            <div class="quantity-info">
              <mat-icon>close</mat-icon>
              <span class="quantity">{{ item.quantity }}</span>
            </div>
            <div class="price">{{ formatPrice(item.price) }}</div>
            <div class="total-price">{{ formatPrice(item.price * item.quantity) }}</div>
          </div>
        </div>
      </div>
    </div>

    <mat-divider></mat-divider>

    <!-- 价格汇总 -->
    <div class="summary-section">
      <h3 class="section-title">
        <mat-icon>calculate</mat-icon>
        Order Summary
      </h3>
      
      <div class="summary-details">
        <div class="summary-row">
          <span class="label">Subtotal:</span>
          <span class="value">{{ formatPrice(getSubtotal()) }}</span>
        </div>

        <div class="summary-row">
          <span class="label">Tax:</span>
          <span class="value">{{ formatPrice(getTaxAmount()) }}</span>
        </div>

        <mat-divider class="summary-divider"></mat-divider>

        <div class="summary-row total-row">
          <span class="label">Total:</span>
          <span class="value total-amount">{{ formatPrice(getTotalAmount()) }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- 弹窗操作按钮 -->
  <div mat-dialog-actions class="dialog-actions">
    <button mat-button
            class="cancel-btn"
            (click)="onCancel()"
            [disabled]="isProcessing">
      <mat-icon>close</mat-icon>
      Cancel
    </button>

    <button mat-raised-button
            color="primary"
            class="confirm-btn"
            (click)="onConfirm()"
            [disabled]="isProcessing">
      <mat-icon *ngIf="!isProcessing">payment</mat-icon>
      <mat-spinner *ngIf="isProcessing" diameter="20"></mat-spinner>
      <span *ngIf="!isProcessing">Confirm Payment</span>
      <span *ngIf="isProcessing">Processing...</span>
    </button>
  </div>
</div>
