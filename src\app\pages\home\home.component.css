.home-container {
  height: 100vh;
  width: 100vw;
  background-image: url('../../../assets/images/background.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

/* 添加半透明遮罩以提高文字可读性 */
.home-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 1;
}

.content-wrapper {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  max-width: 600px;
  padding: 2rem;
}

.welcome-content {
  animation: fadeInUp 1s ease-out;
}

.welcome-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
  line-height: 1.2;
}

.welcome-subtitle {
  font-size: 1.5rem;
  font-weight: 300;
  margin-bottom: 3rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
  opacity: 0.9;
}

.start-ordering-btn {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  border: none;
  padding: 1.2rem 3rem;
  font-size: 1.3rem;
  font-weight: 600;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.start-ordering-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
  background: linear-gradient(135deg, #ee5a24, #ff6b6b);
}

.start-ordering-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.3);
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-title {
    font-size: 2.5rem;
  }
  
  .welcome-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
  }
  
  .start-ordering-btn {
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
  }
  
  .content-wrapper {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .welcome-title {
    font-size: 2rem;
  }

  .welcome-subtitle {
    font-size: 1rem;
  }

  .start-ordering-btn {
    padding: 0.9rem 2rem;
    font-size: 1rem;
  }

  .admin-settings-fab {
    width: 48px !important;
    height: 48px !important;
    bottom: 16px !important;
    right: 16px !important;
  }

  .admin-settings-fab mat-icon {
    font-size: 20px !important;
    width: 20px !important;
    height: 20px !important;
  }
}

/* 管理员设置按钮样式 */
.admin-settings-fab {
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1976d2, #1565c0);
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
  transition: all 0.3s ease;
  z-index: 1000;
  opacity: 0.8;
}

.admin-settings-fab:hover {
  opacity: 1;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(25, 118, 210, 0.4);
  background: linear-gradient(135deg, #1565c0, #0d47a1);
}

.admin-settings-fab:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.3);
}

.admin-settings-fab mat-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
  transition: transform 0.3s ease;
}

.admin-settings-fab:hover mat-icon {
  transform: rotate(90deg);
}
