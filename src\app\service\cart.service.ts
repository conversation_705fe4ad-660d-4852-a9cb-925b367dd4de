import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { DeliOrderingFoodCartClient, CreateFoodCartRequest, FoodCartResponse, InsertFoodCartItemBody, ProductAttributeItem, AddFoodCartItemRequest, DeleteFoodCartItemResponse, UpdateFoodCartItemRequest, DeliOrderingFoodOrderClient, TransactionOrderResponse } from './backoffice';

// 使用API客户端生成的原生类型

// 简化的属性选择接口 - 用于内部状态管理
export interface AttributeSelection {
  groupId: string;
  groupName: string;
  items: Array<{
    id: string;
    name: string;
    price?: number;
  }>;
}

// 新的API属性接口 - 匹配后端更新的结构
export interface SelectedAttribute {
  id: string;
  name: string;
  price: number;
}

// 简化的购物车商品接口
export interface CartItem {
  id: string;
  foodItemId: string;
  name: string;
  description?: string;
  image?: string;
  price: number;
  quantity: number;
  basePrice: number; // 商品原始基础价格
  selectedAttributes: AttributeSelection[];
  memo?: string; // 备注信息
}

// 简化的购物车状态接口
export interface CartState {
  id: string | null;
  items: CartItem[];
  totalAmount: number;
  totalTaxAmount: number; // 新增税额字段
  itemCount: number;
  loading: boolean;
  error: string | null;
}

@Injectable({
  providedIn: 'root'
})
export class CartService {
  // 简化的购物车状态
  private cartState: CartState = {
    id: null,
    items: [],
    totalAmount: 0,
    totalTaxAmount: 0, // 初始化税额为0
    itemCount: 0,
    loading: false,
    error: null
  };

  // 状态主题
  private cartStateSubject = new BehaviorSubject<CartState>(this.cartState);
  private cartSidebarOpenSubject = new BehaviorSubject<boolean>(false);

  // 公共观察者
  public cartState$ = this.cartStateSubject.asObservable();
  public cartSidebarOpen$ = this.cartSidebarOpenSubject.asObservable();

  constructor(
    private cartClient: DeliOrderingFoodCartClient,
    private orderClient: DeliOrderingFoodOrderClient
  ) {}

  /**
   * 检查是否已有购物车
   */
  hasCart(): boolean {
    return this.cartState.id !== null && this.cartState.id !== '';
  }

  /**
   * 查找购物车中是否已存在相同商品
   * @param productConfig 商品配置信息
   * @returns 存在的商品项或null
   */
  findExistingItem(productConfig: any): CartItem | null {
    if (!this.hasCart() || this.cartState.items.length === 0) {
      return null;
    }

    return this.cartState.items.find(item =>
      this.isSameProduct(item, productConfig)
    ) || null;
  }

  /**
   * 判断两个商品是否为同一商品（包括规格）
   */
  private isSameProduct(cartItem: CartItem, productConfig: any): boolean {
    // 首先检查商品ID是否相同
    if (cartItem.foodItemId !== productConfig.foodItemId) {
      return false;
    }

    // 然后检查规格是否完全相同
    return this.areAttributesEqual(cartItem.selectedAttributes, productConfig.selectedAttributes);
  }

  /**
   * 比较两个规格选择是否相同
   */
  private areAttributesEqual(cartAttributes: AttributeSelection[], configAttributes: any[]): boolean {
    if (!cartAttributes && !configAttributes) {
      return true;
    }

    if (!cartAttributes || !configAttributes) {
      return false;
    }

    if (cartAttributes.length !== configAttributes.length) {
      return false;
    }

    // 检查每个规格组是否匹配
    for (const cartAttr of cartAttributes) {
      const matchingConfigAttr = configAttributes.find(
        (configAttr: any) => configAttr.groupId === cartAttr.groupId
      );

      if (!matchingConfigAttr) {
        return false;
      }

      // 检查规格组内的选项是否完全匹配
      if (!this.areAttributeItemsEqual(cartAttr.items, matchingConfigAttr.items)) {
        return false;
      }
    }

    return true;
  }

  /**
   * 比较两个规格选项数组是否相同
   */
  private areAttributeItemsEqual(items1: any[], items2: any[]): boolean {
    if (items1.length !== items2.length) {
      return false;
    }

    // 按ID排序后比较
    const sorted1 = items1.map(item => item.id).sort();
    const sorted2 = items2.map(item => item.id).sort();

    return sorted1.every((id, index) => id === sorted2[index]);
  }

  /**
   * 添加商品到购物车
   * @param storeId 商店ID
   * @param productConfig 商品配置信息
   * @returns Observable<FoodCartResponse>
   */
  addToCart(storeId: number, productConfig: any): Observable<FoodCartResponse> {
    console.log('=== 添加商品到购物车 ===');
    console.log('storeId:', storeId);
    console.log('productConfig:', productConfig);
    console.log('当前购物车状态:', this.cartState);

    if (this.hasCart()) {
      // 检查是否存在相同商品
      const existingItem = this.findExistingItem(productConfig);

      if (existingItem) {
        console.log('发现相同商品，合并数量:', {
          existingItem: existingItem,
          currentQuantity: existingItem.quantity,
          addQuantity: productConfig.quantity,
          newQuantity: existingItem.quantity + productConfig.quantity
        });

        // 合并数量：现有数量 + 要添加的数量
        const newQuantity = existingItem.quantity + productConfig.quantity;
        return this.updateItemQuantity(existingItem, newQuantity);
      } else {
        console.log('未发现相同商品，添加新商品到现有购物车');
        return this.addToExistingCart(storeId, productConfig);
      }
    } else {
      console.log('购物车不存在，创建新购物车');
      return this.createCartWithItem(storeId, productConfig);
    }
  }

  /**
   * 创建购物车并添加商品
   * @param storeId 商店ID
   * @param productConfig 商品配置信息
   * @returns Observable<FoodCartResponse>
   */
  createCartWithItem(storeId: number, productConfig: any): Observable<FoodCartResponse> {
    console.log('开始创建购物车:', { storeId, productConfig });

    // 设置加载状态
    this.updateLoadingState(true);

    // 构建API请求数据 - 使用API文档格式
    const attributeGroups = this.convertToAttributeGroups(productConfig.selectedAttributes);

    // 创建InsertFoodCartItemBody，使用API文档格式的包装对象
    const foodCartItem = new InsertFoodCartItemBody();
    foodCartItem.foodItem_ID = productConfig.foodItemId;
    foodCartItem.quantity = productConfig.quantity;

    // 传入基础价格，让后端基于此计算最终价格
    const basePrice = productConfig.basePrice || 0;
    foodCartItem.price = basePrice;
    console.log('传入基础价格:', basePrice, '来源:', productConfig.basePrice ? 'productConfig.basePrice' : '默认值0');

    // 设置备注信息
    foodCartItem.memo = productConfig.memo || '';
    console.log('传入备注信息:', productConfig.memo);

    // 设置选择的属性
    foodCartItem.selectedAttibutes = attributeGroups;

    const request = new CreateFoodCartRequest({
      storeId: storeId,
      foodCartItem: foodCartItem
    });

    console.log('API请求数据（包含新格式的selectedAttibutes）:', {
      storeId: storeId,
      foodCartItem: {
        foodItem_ID: productConfig.foodItemId,
        price: basePrice,
        quantity: productConfig.quantity,
        selectedAttibutes: attributeGroups
      }
    });

    return this.cartClient.createCart(request).pipe(
      map(response => {
        console.log('创建购物车API响应:', response);
        this.updateCartStateFromResponse(response.result!, productConfig);
        return response.result!;
      }),
      catchError(error => {
        console.error('创建购物车失败:', error);
        this.updateLoadingState(false);
        throw error;
      })
    );
  }

  /**
   * 将本地属性选择格式转换为API要求的格式
   * 新API期望的格式：ProductAttributeItem[]
   */
  private convertToAttributeGroups(selectedAttributes: AttributeSelection[]): ProductAttributeItem[] {
    if (!selectedAttributes || selectedAttributes.length === 0) {
      return [];
    }

    console.log('转换前的属性数据:', selectedAttributes);

    // 将所有选中的属性项扁平化为ProductAttributeItem数组
    const result = selectedAttributes.flatMap(attr =>
      attr.items.map(item => new ProductAttributeItem({
        id: item.id,
        name: item.name,
        price: item.price || 0
      }))
    );

    console.log('转换后的selectedAttibutes:', result);
    return result;
  }

  /**
   * 根据API响应更新购物车状态
   */
  private updateCartStateFromResponse(response: FoodCartResponse, productConfig?: any): void {
    if (!response.cart || !response.item) {
      console.warn('API响应缺少购物车或商品信息');
      return;
    }

    console.log('=== 更新购物车状态 ===');
    console.log('API响应的购物车信息:', response.cart);
    console.log('API响应的商品信息:', response.item);

    // 创建新的购物车商品
    const newCartItem: CartItem = {
      id: response.item.id || '',
      foodItemId: response.item.foodItem_ID || '',
      name: productConfig?.name || response.item.foodItem_ID || 'Unknown Product',
      description: productConfig?.description || '',
      image: productConfig?.image || '',
      price: response.item.price || 0,
      quantity: response.item.quantity || 1,
      basePrice: productConfig?.basePrice || response.item.price || 0,
      selectedAttributes: this.convertFromAttributeGroups(response.item.selectedAttibutes || []),
      memo: response.item.memo || productConfig?.memo || ''
    };

    console.log('=== 创建的购物车商品 ===');
    console.log('newCartItem.selectedAttributes:', newCartItem.selectedAttributes);

    // 更新购物车状态
    this.cartState = {
      id: response.cart.id!,
      items: [newCartItem],
      totalAmount: response.cart.totalAmount || 0,
      totalTaxAmount: response.cart.totalTaxAmount || 0, // 添加税额字段
      itemCount: 1,
      loading: false,
      error: null
    };

    // 发布状态更新
    this.cartStateSubject.next(this.cartState);
  }

  /**
   * 将API的简化属性格式转换为本地格式
   * 新API返回格式：ProductAttributeItem[]
   */
  private convertFromAttributeGroups(selectedAttibutes: ProductAttributeItem[]): AttributeSelection[] {
    console.log('=== 转换API返回的selectedAttibutes ===');
    console.log('输入的selectedAttibutes:', selectedAttibutes);

    if (!selectedAttibutes || selectedAttibutes.length === 0) {
      return [];
    }

    // 由于新API不再有分组概念，我们创建一个默认分组来保持内部数据结构一致
    const result: AttributeSelection[] = [{
      groupId: 'default-group',
      groupName: 'Selected Options',
      items: selectedAttibutes.map((item: ProductAttributeItem) => ({
        id: item.id || '',
        name: item.name || '',
        price: item.price || 0
      }))
    }];

    console.log('转换后的AttributeSelection:', result);
    return result;
  }

  /**
   * 添加商品到现有购物车
   */
  addToExistingCart(storeId: number, productConfig: any): Observable<FoodCartResponse> {
    console.log('添加商品到现有购物车:', { storeId, productConfig, cartId: this.cartState.id });

    // 设置加载状态
    this.updateLoadingState(true);

    // 构建API请求数据
    const attributeGroups = this.convertToAttributeGroups(productConfig.selectedAttributes);

    const foodCartItem = new InsertFoodCartItemBody();
    foodCartItem.foodItem_ID = productConfig.foodItemId;
    foodCartItem.quantity = productConfig.quantity;
    foodCartItem.price = productConfig.basePrice || 0;
    foodCartItem.memo = productConfig.memo || '';
    foodCartItem.selectedAttibutes = attributeGroups;

    const request = new AddFoodCartItemRequest({
      storeId: storeId,
      cartId: this.cartState.id!,
      foodCartItem: foodCartItem
    });

    console.log('添加到现有购物车的API请求:', request);

    return this.cartClient.addCartItem(request).pipe(
      map(response => {
        console.log('添加商品到现有购物车API响应:', response);
        this.updateCartStateAfterAdd(response.result!, productConfig);
        return response.result!;
      }),
      catchError(error => {
        console.error('添加商品到现有购物车失败:', error);
        this.updateLoadingState(false);
        throw error;
      })
    );
  }

  /**
   * 添加商品后更新购物车状态
   */
  private updateCartStateAfterAdd(response: FoodCartResponse, productConfig: any): void {
    if (!response.cart || !response.item) {
      console.warn('API响应缺少购物车或商品信息');
      return;
    }

    console.log('=== 添加商品后更新购物车状态 ===');
    console.log('API响应:', response);

    // 创建新的购物车商品
    const newCartItem: CartItem = {
      id: response.item.id || '',
      foodItemId: response.item.foodItem_ID || '',
      name: productConfig?.name || response.item.foodItem_ID || 'Unknown Product',
      description: productConfig?.description || '',
      image: productConfig?.image || '',
      price: response.item.price || 0,
      quantity: response.item.quantity || 1,
      basePrice: productConfig?.basePrice || response.item.price || 0,
      selectedAttributes: this.convertFromAttributeGroups(response.item.selectedAttibutes || []),
      memo: response.item.memo || productConfig?.memo || ''
    };

    console.log('=== 创建的新购物车商品 ===');
    console.log('newCartItem:', newCartItem);

    // 添加到现有购物车商品列表
    this.cartState.items.push(newCartItem);

    // 更新购物车总信息
    this.cartState.totalAmount = response.cart.totalAmount || 0;
    this.cartState.totalTaxAmount = response.cart.totalTaxAmount || 0; // 添加税额字段
    this.cartState.itemCount = this.cartState.items.length;
    this.cartState.loading = false;
    this.cartState.error = null;

    // 发布状态更新
    this.cartStateSubject.next(this.cartState);
  }

  /**
   * 删除购物车商品
   */
  removeItem(item: CartItem): Observable<DeleteFoodCartItemResponse> {
    console.log('删除购物车商品:', item);

    // 设置加载状态
    this.updateLoadingState(true);

    const storeId = 2; // 固定使用storeId=2

    return this.cartClient.deleteCartItem(storeId, item.id).pipe(
      map(response => {
        console.log('删除商品API响应:', response);
        this.updateCartStateAfterDelete(item, response.result!);
        return response.result!;
      }),
      catchError(error => {
        console.error('删除商品失败:', error);
        this.updateLoadingState(false);
        throw error;
      })
    );
  }

  /**
   * 删除商品后更新购物车状态
   */
  private updateCartStateAfterDelete(deletedItem: CartItem, response: DeleteFoodCartItemResponse): void {
    // 从商品列表中移除该商品
    this.cartState.items = this.cartState.items.filter(item => item.id !== deletedItem.id);

    // 更新购物车总金额和税额（从新的API响应结构获取）
    this.cartState.totalAmount = response.cart?.totalAmount || 0;
    this.cartState.totalTaxAmount = response.cart?.totalTaxAmount || 0;

    // 更新商品数量
    this.cartState.itemCount = this.cartState.items.length;

    // 如果购物车为空，重置购物车ID
    if (this.cartState.items.length === 0) {
      console.log('购物车已清空，重置购物车状态');
      this.cartState.id = null;
      this.cartState.totalAmount = 0;
      this.cartState.totalTaxAmount = 0;
      this.cartState.itemCount = 0;
    }

    this.cartState.loading = false;
    this.cartState.error = null;

    // 发布状态更新
    this.cartStateSubject.next(this.cartState);
  }

  /**
   * 更新购物车商品数量
   */
  updateItemQuantity(item: CartItem, newQuantity: number): Observable<FoodCartResponse> {
    if (newQuantity < 1) {
      throw new Error('商品数量不能小于1');
    }

    console.log('更新商品数量:', {
      cartItemId: item.id,
      itemName: item.name,
      oldQuantity: item.quantity,
      newQuantity: newQuantity
    });

    // 设置加载状态
    this.updateLoadingState(true);

    const storeId = 2; // 固定使用storeId=2

    // 构建API请求数据 - 使用正确的API格式
    const apiAttributes = this.convertToAttributeGroups(item.selectedAttributes);

    const request = new UpdateFoodCartItemRequest({
      storeId: storeId,
      cartItemId: item.id,
      quantity: newQuantity,
      price: item.basePrice, // 使用存储的原始基础价格
      memo: item.memo || '',
      selectedAttibutes: apiAttributes
    });

    console.log('更新数量API请求:', request);

    return this.cartClient.updateCartItem(request).pipe(
      map(response => {
        console.log('更新数量API响应:', response);
        this.updateCartStateAfterQuantityUpdate(item, newQuantity, response.result!);
        return response.result!;
      }),
      catchError(error => {
        console.error('更新商品数量失败:', error);
        this.updateLoadingState(false);
        throw error;
      })
    );
  }

  /**
   * 更新数量后更新购物车状态
   */
  private updateCartStateAfterQuantityUpdate(item: CartItem, newQuantity: number, response: FoodCartResponse): void {
    // 找到并更新对应的商品
    const itemIndex = this.cartState.items.findIndex(cartItem => cartItem.id === item.id);
    if (itemIndex !== -1) {
      this.cartState.items[itemIndex].quantity = newQuantity;
      // 更新商品价格（如果API返回了新价格）
      if (response.item && response.item.price) {
        this.cartState.items[itemIndex].price = response.item.price;
      }
    }

    // 更新购物车总信息
    this.cartState.totalAmount = response.cart?.totalAmount || 0;
    this.cartState.totalTaxAmount = response.cart?.totalTaxAmount || 0; // 添加税额字段
    this.cartState.itemCount = this.cartState.items.reduce((total, item) => total + item.quantity, 0);
    this.cartState.loading = false;
    this.cartState.error = null;

    // 发布状态更新
    this.cartStateSubject.next(this.cartState);
  }

  /**
   * 设置加载状态
   */
  private updateLoadingState(loading: boolean): void {
    this.cartState.loading = loading;
    this.cartStateSubject.next(this.cartState);
  }

  /**
   * 获取购物车商品总数
   */
  getItemCount(): number {
    return this.cartState.itemCount;
  }

  /**
   * 获取购物车总金额
   */
  getTotalAmount(): number {
    return this.cartState.totalAmount;
  }

  /**
   * 获取购物车总税额
   */
  getTotalTaxAmount(): number {
    return this.cartState.totalTaxAmount;
  }

  /**
   * 结算购物车
   * @param storeId 商店ID
   * @returns Observable<TransactionOrderResponse>
   */
  checkout(storeId: number): Observable<TransactionOrderResponse> {
    if (!this.cartState.id) {
      throw new Error('购物车ID不存在，无法进行结算');
    }

    if (this.cartState.items.length === 0) {
      throw new Error('购物车为空，无法进行结算');
    }

    console.log('=== 开始结算 ===');
    console.log('storeId:', storeId);
    console.log('cartId:', this.cartState.id);
    console.log('购物车商品数量:', this.cartState.items.length);
    console.log('购物车总金额:', this.cartState.totalAmount);
    console.log('购物车状态:', this.cartState);
    console.log('API URL将会是:', `/api/Deliordering/food/order/${storeId}/${this.cartState.id}`);

    this.cartState.loading = true;
    this.cartStateSubject.next(this.cartState);

    return this.orderClient.createOrder(storeId, this.cartState.id).pipe(
      map(response => {
        console.log('=== 结算成功 ===');
        console.log('完整响应:', response);
        console.log('结算结果:', response.result);

        if (!response.result) {
          console.error('⚠️ API返回成功但result为空!');
          throw new Error('API返回成功但订单数据为空');
        }

        console.log('✅ 订单数据详情:', {
          transactionID: response.result.transactionID,
          totalAmount: response.result.totalAmount,
          products: response.result.products
        });

        // 调用Android打印接口传递订单数据
        this.callAndroidPrintInterface(response.result);

        // 临时存储订单结果，供结果页面使用（用完即删）
        try {
          sessionStorage.setItem('tempOrderResult', JSON.stringify(response.result));
          console.log('📦 订单结果已临时存储');
        } catch (error) {
          console.error('临时存储订单结果失败:', error);
        }

        // 结算成功后清空购物车
        this.clearCart();

        return response.result!;
      }),
      catchError(error => {
        console.error('=== 结算失败 ===');
        console.error('错误详情:', error);
        console.error('错误状态码:', error.status);
        console.error('错误消息:', error.message);
        console.error('错误响应体:', error.response);

        this.cartState.loading = false;
        this.cartStateSubject.next(this.cartState);
        throw error;
      })
    );
  }

  /**
   * 清空购物车状态
   */
  private clearCart(): void {
    this.cartState = {
      id: null,
      items: [],
      totalAmount: 0,
      totalTaxAmount: 0,
      itemCount: 0,
      loading: false,
      error: null
    };
    this.cartStateSubject.next(this.cartState);
    console.log('购物车已清空');
  }

  /**
   * 设置错误信息
   */
  setError(error: string): void {
    this.cartState.error = error;
    this.cartState.loading = false;
    this.cartStateSubject.next(this.cartState);
  }

  /**
   * 清除错误信息
   */
  clearError(): void {
    this.cartState.error = null;
    this.cartStateSubject.next(this.cartState);
  }

  /**
   * 打开购物车侧边栏
   */
  openCartSidebar(): void {
    this.cartSidebarOpenSubject.next(true);
  }

  /**
   * 关闭购物车侧边栏
   */
  closeCartSidebar(): void {
    this.cartSidebarOpenSubject.next(false);
  }

  /**
   * 切换购物车侧边栏状态
   */
  toggleCartSidebar(): void {
    this.cartSidebarOpenSubject.next(!this.cartSidebarOpenSubject.value);
  }



  /**
   * 增加商品数量
   */
  increaseQuantity(item: CartItem): Observable<FoodCartResponse> {
    return this.updateItemQuantity(item, item.quantity + 1);
  }

  /**
   * 减少商品数量
   */
  decreaseQuantity(item: CartItem): Observable<FoodCartResponse> {
    if (item.quantity <= 1) {
      throw new Error('商品数量不能小于1');
    }
    return this.updateItemQuantity(item, item.quantity - 1);
  }

  /**
   * 添加商品到购物车（兼容旧接口名）
   */
  addItemToCart(storeId: number, productConfig: any): Observable<FoodCartResponse> {
    return this.addToCart(storeId, productConfig);
  }

  /**
   * 更新购物车商品（兼容旧接口）
   */
  updateCartItem(storeId: number, cartItemId: string, productConfig: any): Observable<FoodCartResponse> {
    // 找到对应的购物车商品
    const item = this.cartState.items.find(item => item.id === cartItemId);
    if (!item) {
      throw new Error('No corresponding shopping cart item was found.');
    }

    // 构建API请求数据
    const attributeGroups = this.convertToAttributeGroups(productConfig.selectedAttributes);

    const request = new UpdateFoodCartItemRequest({
      storeId: storeId,
      cartItemId: cartItemId,
      quantity: productConfig.quantity,
      price: productConfig.basePrice || item.basePrice,
      memo: productConfig.memo || '',
      selectedAttibutes: attributeGroups
    });

    console.log('更新购物车商品API请求:', request);

    return this.cartClient.updateCartItem(request).pipe(
      map(response => {
        console.log('更新购物车商品API响应:', response);

        // 更新本地状态
        const itemIndex = this.cartState.items.findIndex(item => item.id === cartItemId);
        if (itemIndex !== -1) {
          this.cartState.items[itemIndex] = {
            ...this.cartState.items[itemIndex],
            quantity: productConfig.quantity,
            price: response.result?.item?.price || this.cartState.items[itemIndex].price,
            memo: response.result?.item?.memo || productConfig.memo || '',
            selectedAttributes: this.convertFromAttributeGroups(attributeGroups)
          };
        }

        // 更新购物车总信息
        if (response.result?.cart) {
          this.cartState.totalAmount = response.result.cart.totalAmount || 0;
          this.cartState.totalTaxAmount = response.result.cart.totalTaxAmount || 0;
        }

        this.cartState.loading = false;
        this.cartStateSubject.next(this.cartState);

        return response.result!;
      }),
      catchError(error => {
        console.error('更新购物车商品失败:', error);
        this.updateLoadingState(false);
        throw error;
      })
    );
  }

  /**
   * 调用Android WebView接口传递订单数据
   * @param orderData 完整的订单数据
   */
  private callAndroidPrintInterface(orderData: TransactionOrderResponse): void {
    try {
      console.log('🤖 准备调用Android打印接口...');
      console.log('订单数据:', orderData);

      // 检查Android WebView接口是否存在
      if (typeof (window as any).WebAndroid !== 'undefined' && (window as any).WebAndroid) {
        console.log('✅ 检测到Android WebView接口');

        // 将订单数据转换为JSON字符串
        const orderJson = JSON.stringify(orderData);
        console.log('📄 准备传递的JSON数据:', orderJson);

        // 调用Android接口
        // 您需要根据实际的Android接口方法名进行调整
        if (typeof (window as any).WebAndroid.onOrderCompleted === 'function') {
          (window as any).WebAndroid.onOrderCompleted(orderJson);
          console.log('✅ 成功调用Android打印接口: onOrderCompleted');
        } else if (typeof (window as any).WebAndroid.printOrder === 'function') {
          (window as any).WebAndroid.printOrder(orderJson);
          console.log('✅ 成功调用Android打印接口: printOrder');
        } else if (typeof (window as any).WebAndroid.receiveOrderData === 'function') {
          (window as any).WebAndroid.receiveOrderData(orderJson);
          console.log('✅ 成功调用Android打印接口: receiveOrderData');
        } else {
          console.warn('⚠️ Android WebView接口存在但未找到预期的方法');
          console.log('可用的方法:', Object.keys((window as any).WebAndroid));

          // 尝试通用调用（如果有通用方法）
          if (typeof (window as any).WebAndroid.call === 'function') {
            (window as any).WebAndroid.call('orderCompleted', orderJson);
            console.log('✅ 通过通用方法调用Android接口');
          }
        }
      } else {
        console.log('ℹ️ 未检测到Android WebView接口，可能运行在浏览器环境中');
        console.log('window.WebAndroid:', (window as any).WebAndroid);
      }
    } catch (error) {
      console.error('❌ 调用Android接口时发生错误:', error);
      // 不抛出错误，避免影响正常的结算流程
    }
  }
}
