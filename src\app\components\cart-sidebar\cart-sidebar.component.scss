// 遮罩层
.cart-sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1500;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &.open {
    opacity: 1;
    visibility: visible;
  }
}

// 购物车底部面板
.cart-sidebar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  max-height: 70vh;
  background: white;
  z-index: 1600;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;

  &.open {
    transform: translateY(0) !important;
  }
}

// 头部 - 底部面板样式
.cart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px 12px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
  position: relative;

  // 添加拖拽指示器
  &::before {
    content: '';
    position: absolute;
    top: 8px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 4px;
    background: #d1d5db;
    border-radius: 2px;
  }

  h2 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 500;
    color: #1f2937;
  }

  .cart-total {
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    .total-label {
      font-size: 0.875rem;
      color: #6b7280;
      margin-bottom: 2px;
    }

    .total-amount {
      font-size: 1.25rem;
      font-weight: 700;
      color: #1f2937;
    }

    .tax-info {
      margin-top: 4px;
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      .tax-label {
        font-size: 0.75rem;
        color: #9ca3af;
        margin-bottom: 1px;
      }

      .tax-amount {
        font-size: 0.875rem;
        font-weight: 500;
        color: #6b7280;
      }
    }
  }
}

// 内容区域
.cart-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

// 加载状态
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 24px;
  
  p {
    margin-top: 16px;
    color: #6b7280;
  }
}

// 错误状态
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 24px;
  text-align: center;
  
  mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    color: #ef4444;
    margin-bottom: 16px;
  }
  
  p {
    color: #6b7280;
    margin-bottom: 16px;
  }
}

// 空购物车状态
.empty-cart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 24px;
  text-align: center;
  
  .empty-icon {
    font-size: 64px;
    width: 64px;
    height: 64px;
    color: #d1d5db;
    margin-bottom: 24px;
  }
  
  h3 {
    margin: 0 0 12px 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #374151;
  }
  
  p {
    margin: 0;
    color: #6b7280;
    font-size: 0.875rem;
  }
}

// 购物车商品列表
.cart-items {
  padding: 0;

  .items-container {
    padding: 0;
  }

  .cart-item {
    border-bottom: 1px solid #f1f5f9;

    &:last-child {
      border-bottom: none;
    }

    .item-content {
      display: flex;
      align-items: center;
      padding: 16px 20px;
      width: 100%;
      gap: 12px;
      background: white;
      transition: background-color 0.2s ease;
      min-height: 80px;

      &:hover {
        background: #fafbfc;
      }

      .item-main-info {
        display: flex;
        align-items: center;
        gap: 12px;
        flex: 1;
        cursor: pointer;
        padding: 8px;
        margin: -8px;
        border-radius: 8px;
        transition: background-color 0.2s ease;

        &:hover {
          background: #f8fafc;
        }

        &:active {
          background: #f1f5f9;
        }
      }
    }
  }
}

// 商品图片
.item-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

// 商品信息
.item-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 4px;
  min-height: 60px;

  .item-name {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: #1f2937;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .item-specs {
    font-size: 0.75rem;
    color: #64748b;
    line-height: 1.2;
    margin: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .item-memo {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.75rem;
    line-height: 1.3;
    margin: 4px 0;
    padding: 4px 8px;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.08) 0%, rgba(6, 182, 212, 0.08) 100%);
    border: 1px solid rgba(16, 185, 129, 0.2);
    border-radius: 6px;
    color: #065f46;
    font-weight: 500;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    transition: all 0.2s ease;
    backdrop-filter: blur(4px);
    position: relative;
    
    // 添加微妙的光影效果
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.5) 0%, transparent 50%);
      border-radius: 6px;
      pointer-events: none;
    }
    
    // 悬停效果
    &:hover {
      background: linear-gradient(135deg, rgba(16, 185, 129, 0.12) 0%, rgba(6, 182, 212, 0.12) 100%);
      border-color: rgba(16, 185, 129, 0.3);
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(16, 185, 129, 0.15);
    }

    .memo-icon {
      font-size: 13px;
      width: 13px;
      height: 13px;
      color: #059669;
      flex-shrink: 0;
      position: relative;
      z-index: 1;
    }
    
    span {
      position: relative;
      z-index: 1;
      font-weight: 500;
      letter-spacing: -0.01em;
    }
  }

  .item-price {
    font-size: 0.875rem;
    font-weight: 600;
    color: #2563eb;
  }
}

// 优化的数量控制器
.quantity-controls {
  display: flex;
  align-items: center;
  gap: 0;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
    border-color: #cbd5e1;
  }

  .quantity-btn {
    width: 32px;
    height: 32px;
    min-width: 32px;
    border: none;
    background: transparent;
    color: #64748b;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    &:hover:not(:disabled) {
      background: linear-gradient(145deg, #f1f5f9 0%, #e2e8f0 100%);
      color: #475569;
      transform: scale(1.1);
    }

    &:active:not(:disabled) {
      background: linear-gradient(145deg, #e2e8f0 0%, #cbd5e1 100%);
      transform: scale(0.95);
    }

    &:disabled {
      color: #cbd5e1;
      cursor: not-allowed;
      transform: none;
    }

    // 添加点击波纹效果
    &::after {
      content: '';
      position: absolute;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background: rgba(59, 130, 246, 0.3);
      transform: scale(0);
      transition: transform 0.2s ease;
    }

    &:active:not(:disabled)::after {
      transform: scale(1);
      transition: transform 0.1s ease;
    }
  }

  .quantity {
    font-size: 0.9375rem;
    font-weight: 700;
    min-width: 36px;
    text-align: center;
    color: #1e293b;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 32px;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    border-left: 1px solid #e2e8f0;
    border-right: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    position: relative;

    // 数量变化动画
    &.quantity-changed {
      animation: quantityPulse 0.4s ease-out;
    }
  }
}

// 数量变化脉冲动画
@keyframes quantityPulse {
  0% {
    transform: scale(1);
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  }
  50% {
    transform: scale(1.1);
    background: linear-gradient(145deg, #dbeafe 0%, #bfdbfe 100%);
    color: #1d4ed8;
  }
  100% {
    transform: scale(1);
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  }
}



// 底部结算区域
.cart-footer {
  border-top: 1px solid #e5e7eb;
  padding: 20px 24px;
  background: #f8fafc;

  .checkout-btn {
    width: 100%;
    height: 48px;
    font-size: 1rem;
    font-weight: 600;
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
    }

    &:active:not(:disabled) {
      transform: translateY(0);
    }

    &:disabled {
      background: #9ca3af !important;
      color: #6b7280 !important;
      cursor: not-allowed !important;
      transform: none !important;
      box-shadow: none !important;
    }
  }
}

// 响应式设计优化
@media (max-width: 768px) {
  .cart-sidebar {
    max-height: 80vh; // 移动端增加最大高度

    .cart-items .cart-item .item-content {
      padding: 16px;
      gap: 10px;
    }

    .item-left {
      gap: 10px;
    }

    .item-image {
      width: 45px;
      height: 45px;
    }

    .item-info {
      .item-name {
        font-size: 0.85rem;
        margin-bottom: 3px;
      }

      .item-specs {
        font-size: 0.7rem;
      }
      
    }

    .item-right {
      gap: 6px;
    }

    .item-price {
      font-size: 0.9rem;
    }

    .item-controls {
      gap: 8px;
    }

    .item-actions {
      .item-subtotal {
        font-size: 1rem;
      }

      .controls-section {
        gap: 10px;

        .quantity-controls {
          .quantity-btn {
            width: 28px;
            height: 28px;
            min-width: 28px;
            border-radius: 10px;
            font-size: 16px;
          }

          .quantity {
            min-width: 36px;
            font-size: 0.85rem;
            height: 28px;
          }
        }


      }
    }
  }

  .cart-footer {
    padding: 20px 16px;

    .checkout-btn {
      height: 48px;
    }
  }
}

// 平板设备优化
@media (max-width: 1024px) and (min-width: 769px) {
  .cart-sidebar {
    width: 380px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .cart-sidebar {
    width: 100vw;
    
    .cart-header {
      padding: 16px 20px;
      
      h2 {
        font-size: 1.25rem;
      }
    }
    
    .cart-items .cart-item .item-content {
      padding: 16px 20px;
      gap: 12px;
    }
    
    .item-image {
      width: 50px;
      height: 50px;
    }
    
    .cart-footer {
      padding: 20px;
    }
  }
}

// 顶部固定的同步进度条 - 适配底部面板
.top-sync-progress {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1610;
  height: 2px !important;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  overflow: hidden;

  ::ng-deep {
    .mat-progress-bar-buffer {
      background-color: transparent;
    }

    .mat-progress-bar-fill::after {
      background-color: #2563eb;
    }

    .mat-progress-bar-background {
      background-color: rgba(37, 99, 235, 0.1);
    }
  }
}

// 同步错误横幅 - 最小化空间占用
.sync-error-banner {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #fef2f2;
  border-left: 3px solid #dc2626;
  margin: 0 0 8px 0;
  font-size: 0.75rem;
  color: #dc2626;
  min-height: 32px;

  .error-icon {
    font-size: 14px;
    width: 14px;
    height: 14px;
  }
}

// 商品同步状态指示器
.sync-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  flex-shrink: 0;

  .sync-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
    color: #2563eb;
    animation: spin 1s linear infinite;
  }
}


// 旋转动画
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 错误信息显示
.error-message {
  background: #fee2e2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 12px 16px;
  margin: 16px;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;

  .error-close-btn {
    background: none;
    border: none;
    color: #dc2626;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #fecaca;
    }
  }
}
