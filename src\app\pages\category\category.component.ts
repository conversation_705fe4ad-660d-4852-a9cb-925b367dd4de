import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, NgZone, OnInit, OnDestroy, ChangeDetectorRef, OnChanges, SimpleChanges } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { CommonModule } from '@angular/common';
import { Platform } from '@angular/cdk/platform';
import { MatDialog } from '@angular/material/dialog';
import { Food, Category, FoodImage } from '../../data/food.data';
import { DeliOrderingFoodItemClient, FoodItemDto, DeliOrderingFoodItemImageGalleryClient, FoodImageChildDto } from '../../service/backoffice';
import { FoodDetailDialogComponent } from '../../components/food-detail-dialog/food-detail-dialog.component';
import { StorageService } from '../../service/storage.service';

@Component({
  selector: 'app-category',
  templateUrl: './category.component.html',
  styleUrls: ['./category.component.css'],
  standalone: true,
  imports: [CommonModule, MatCardModule, MatButtonModule, FoodDetailDialogComponent],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CategoryComponent implements OnInit, OnDestroy, OnChanges {
  @Input() categories: Category[] = [];
  @Input() isLoading = false;
  private readonly loadedImages = new Set<string>();
  @Output() categoryChange = new EventEmitter<number>();
  private observer: IntersectionObserver | null = null;

  // 食物数据缓存
  private foodCache = new Map<string, Food[]>();
  private loadingCategories = new Set<string>();
  // 图片数据缓存
  private imageCache = new Map<string, FoodImage[]>();
  private loadingImages = new Set<string>();


  // 防抖相关状态
  private categoryChangeTimeout: any = null;
  private lastEmittedCategoryIndex = -1;
  private scrollThrottleTimeout: any = null;

  constructor(
    private ngZone: NgZone,
    private foodItemClient: DeliOrderingFoodItemClient,
    private imageGalleryClient: DeliOrderingFoodItemImageGalleryClient,
    private cdr: ChangeDetectorRef,
    private platform: Platform,
    private dialog: MatDialog,
    private storageService: StorageService
  ) {}

  ngOnInit() {
    window.addEventListener('resize', this.onResize);
    // 添加滚动事件监听器来处理底部检测
    this.setupScrollListener();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['categories'] && this.categories.length > 0) {
      // 当分类数据变化时，重新设置观察器
      setTimeout(() => {
        this.setupIntersectionObserver();
      }, 100); // 给DOM一些时间来渲染
    }
  }

  ngOnDestroy() {
    if (this.observer) {
      this.observer.disconnect();
    }
    this.loadedImages.clear();
    window.removeEventListener('resize', this.onResize);
    // 移除滚动事件监听器
    const scrollContainer = document.querySelector('.food-sections');
    if (scrollContainer) {
      scrollContainer.removeEventListener('scroll', this.onScroll);
    }
    // 清理防抖定时器
    if (this.categoryChangeTimeout) {
      clearTimeout(this.categoryChangeTimeout);
      this.categoryChangeTimeout = null;
    }
    // 清理滚动节流定时器
    if (this.scrollThrottleTimeout) {
      clearTimeout(this.scrollThrottleTimeout);
      this.scrollThrottleTimeout = null;
    }
  }

  private onResize = () => {
    if (this.observer) {
      this.observer.disconnect();
      this.setupIntersectionObserver();
    }
  };

  onCategorySelect(index: number): void {
    this.categoryChange.emit(index);
  }

  /**
   * 防抖的分类切换方法
   * @param index 分类索引
   * @param immediate 是否立即执行（用于底部检测等特殊情况）
   */
  private debouncedCategoryChange(index: number, immediate: boolean = false): void {
    // 如果索引没有变化，不需要处理
    if (this.lastEmittedCategoryIndex === index) {
      return;
    }

    // 清除之前的定时器
    if (this.categoryChangeTimeout) {
      clearTimeout(this.categoryChangeTimeout);
    }

    if (immediate) {
      // 立即执行
      this.lastEmittedCategoryIndex = index;
      this.categoryChange.emit(index);
    } else {
      // 防抖执行
      this.categoryChangeTimeout = setTimeout(() => {
        this.lastEmittedCategoryIndex = index;
        this.categoryChange.emit(index);
        this.categoryChangeTimeout = null;
      }, 150); // 150ms 防抖延迟
    }
  }

  private setupIntersectionObserver() {
    // 先断开之前的观察器
    if (this.observer) {
      this.observer.disconnect();
    }

    const options = {
      threshold: [0.2, 0.3, 0.4], // 减少阈值数量，只在较明显可见时触发
      rootMargin: '-10% 0px -40% 0px' // 适中的边距设置
    };

    this.observer = new IntersectionObserver((entries) => {
      let maxRatio = 0;
      let mostVisibleSection: IntersectionObserverEntry | null = null;

      for (const entry of entries) {
        if (entry.isIntersecting && entry.intersectionRatio > maxRatio) {
          maxRatio = entry.intersectionRatio;
          mostVisibleSection = entry;
        }
      }

      if (mostVisibleSection) {
        const categoryId = mostVisibleSection.target.id;
        const index = this.categories.findIndex(cat => cat.route.substring(1) === categoryId);
        if (index !== -1) {
          this.ngZone.run(() => {
            this.debouncedCategoryChange(index);
          });
        }
      }
    }, options);

    // 使用更长的延迟确保DOM完全渲染
    setTimeout(() => {
      const sections = document.querySelectorAll('.category-section');
      sections.forEach((section) => {
        if (this.observer) {
          this.observer.observe(section);
        }
      });
    }, 200);
  }

  getFoodsByCategory(categoryRoute: string): Food[] {
    // 根据路由找到对应的分类ID
    const category = this.categories.find(cat => cat.route === categoryRoute);
    if (!category) {
      return [];
    }

    // 检查缓存
    if (this.foodCache.has(category.id)) {
      return this.foodCache.get(category.id)!;
    }

    // 如果正在加载，返回空数组
    if (this.loadingCategories.has(category.id)) {
      return [];
    }

    // 开始加载食物数据
    this.loadFoodItems(category.id);
    return [];
  }

  private loadFoodItems(categoryId: string): void {
    this.loadingCategories.add(categoryId);

    this.foodItemClient.getFoodItems(2, categoryId).subscribe({
      next: (response) => {
        console.log('商品列表API返回:', response.result);

        if (response.result) {
          const foods = this.convertFoodItemsToFoods(response.result, categoryId);
          this.foodCache.set(categoryId, foods);
          this.loadingCategories.delete(categoryId);
          // 触发变更检测以更新视图
          this.cdr.detectChanges();
        }
      },
      error: (error) => {
        console.error('Failed to load food data:', error);
        this.loadingCategories.delete(categoryId);
        // 设置空数组到缓存，避免重复请求
        this.foodCache.set(categoryId, []);
      }
    });
  }

  private convertFoodItemsToFoods(foodItems: FoodItemDto[], categoryId: string): Food[] {
    return foodItems.map(item => {
      const food: Food = {
        id: item.id || '',
        name: item.description || 'Unknown Product', // 使用 description 作为商品名称
        description: item.productDetails || '', // 使用 productDetails 作为商品描述
        price: item.price || 0,
        image: this.ensureHttpsUrl(item.foodImagePath) || '/assets/images/placeholder-food.svg',
        categoryId: categoryId,
        images: [], // Initialize as empty array, load asynchronously later
        pluId: item.plU_ID, // 添加PLU ID
        barcode: item.barcode // 添加条形码
      };

      // Asynchronously load product images
      if (item.id) {
        this.loadFoodImages(item.id);
      }

      return food;
    });
  }

  /**
   * 加载商品附图
   * @param foodId 商品ID
   */
  private loadFoodImages(foodId: string): void {
    // Check cache
    if (this.imageCache.has(foodId) || this.loadingImages.has(foodId)) {
      return;
    }

    this.loadingImages.add(foodId);

    this.imageGalleryClient.getImages(2, foodId).subscribe({
      next: (response) => {
        if (response.result) {
          const images = this.convertFoodImageChildDtosToFoodImages(response.result);
          this.imageCache.set(foodId, images);
          this.loadingImages.delete(foodId);

          // Update images field for corresponding product
          this.updateFoodImages(foodId, images);
          this.cdr.detectChanges();
        }
      },
      error: (error) => {
        console.error(`Failed to load images for product ${foodId}:`, error);
        this.loadingImages.delete(foodId);
        this.imageCache.set(foodId, []); // Set empty array to avoid repeated requests
      }
    });
  }

  /**
   * 处理URL，保持原始协议不变
   * @param url 原始URL
   * @returns string 原始URL（不做任何协议转换）
   */
  private ensureHttpsUrl(url: string | undefined): string | undefined {
    // 直接返回原始URL，不做任何协议转换
    return url;
  }

  /**
   * 转换API图片数据为组件使用的格式
   * @param imageChildDtos API返回的图片数据
   * @returns FoodImage[] 转换后的图片数据
   */
  private convertFoodImageChildDtosToFoodImages(imageChildDtos: FoodImageChildDto[]): FoodImage[] {
    return imageChildDtos
      .filter(dto => dto.imagePath) // Filter out items without image paths
      .map(dto => ({
        id: dto.id || '',
        imagePath: this.ensureHttpsUrl(dto.imagePath) || '',
        sortOrder: dto.sortOrder || 0
      }))
      .sort((a, b) => a.sortOrder - b.sortOrder); // Sort by sort order field
  }

  /**
   * 更新指定商品的附图数据
   * @param foodId 商品ID
   * @param images 图片数据
   */
  private updateFoodImages(foodId: string, images: FoodImage[]): void {
    // 遍历所有缓存的食物数据，找到对应商品并更新其images字段
    for (const [categoryId, foods] of this.foodCache.entries()) {
      const food = foods.find(f => f.id === foodId);
      if (food) {
        food.images = images;
        break;
      }
    }
  }

  isImageLoaded(imageUrl: string): boolean {
    return this.loadedImages.has(imageUrl);
  }

  onImageLoad(imageUrl: string): void {
    this.loadedImages.add(imageUrl);
  }

  /**
   * 图片加载错误处理
   * @param event 错误事件
   * @param imagePath 图片路径
   */
  onImageError(event: Event, imagePath: string): void {
    console.warn('Failed to load image:', imagePath);
    const imgElement = event.target as HTMLImageElement;
    if (imgElement) {
      // 设置默认占位图
      imgElement.src = '/assets/images/placeholder-food.svg';
      // 标记为已加载，避免无限重试
      this.loadedImages.add(imagePath);
    }
  }

  trackByCategories(_: number, category: Category): string {
    return category.route;
  }

  trackByFoods(_: number, food: Food): string {
    return food.id;
  }

  isLoadingFoodsForCategory(categoryId: string): boolean {
    return this.loadingCategories.has(categoryId);
  }

  /**
   * 获取商品的附图
   * @param foodId 商品ID
   * @returns FoodImage[] 商品附图数组
   */
  getFoodImages(foodId: string): FoodImage[] {
    return this.imageCache.get(foodId) || [];
  }

  /**
   * 检查商品附图是否正在加载
   * @param foodId 商品ID
   * @returns boolean 是否正在加载
   */
  isLoadingFoodImages(foodId: string): boolean {
    return this.loadingImages.has(foodId);
  }

  /**
   * 处理商品点击事件，打开详情弹窗
   * @param food 商品对象
   */
  onFoodClick(food: Food): void {
    // 从本地存储获取storeId
    const storeIdStr = this.storageService.getStoreId();
    if (!storeIdStr) {
      console.error('未找到storeId，请先在管理员设置中配置');
      alert('The store ID has not been set. Please contact the administrator to configure it.');
      return;
    }

    const storeId = parseInt(storeIdStr, 10);
    if (isNaN(storeId)) {
      console.error('storeId格式错误:', storeIdStr);
      alert('Store ID format error, please contact administrator to reconfigure');
      return;
    }

    console.log('使用storeId打开商品详情:', storeId);

    const dialogRef = this.dialog.open(FoodDetailDialogComponent, {
      maxWidth: '1300px',
      width: '95vw',
      maxHeight: '117vh',
      panelClass: 'food-detail-dialog-container',
      hasBackdrop: true,
      disableClose: false,
      data: {
        food: food,
        storeId: storeId // 使用从本地存储获取的商店ID
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && result.action === 'add-to-cart-success') {
        console.log('商品已成功添加到购物车:', result.productConfig);
        console.log('购物车响应:', result.cartResponse);
        // 这里可以添加成功提示或其他UI反馈
      }
    });
  }









  /**
   * 设置滚动监听器来处理底部检测
   */
  private setupScrollListener(): void {
    setTimeout(() => {
      const scrollContainer = document.querySelector('.food-sections');
      if (scrollContainer) {
        scrollContainer.addEventListener('scroll', this.onScroll);
      }
    }, 300); // 确保DOM已渲染
  }

  /**
   * 滚动事件处理器（节流优化）
   */
  private onScroll = (): void => {
    // 使用节流避免频繁触发
    if (this.scrollThrottleTimeout) {
      return;
    }

    this.scrollThrottleTimeout = setTimeout(() => {
      const scrollContainer = document.querySelector('.food-sections');
      if (!scrollContainer || this.categories.length === 0) {
        this.scrollThrottleTimeout = null;
        return;
      }

      const scrollTop = scrollContainer.scrollTop;
      const scrollHeight = scrollContainer.scrollHeight;
      const clientHeight = scrollContainer.clientHeight;

      // 检查是否滚动到底部（允许10px的误差）
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;

      if (isAtBottom) {
        // 滚动到底部时，激活最后一个分类（立即执行，不防抖）
        const lastCategoryIndex = this.categories.length - 1;
        this.ngZone.run(() => {
          this.debouncedCategoryChange(lastCategoryIndex, true); // 立即执行
        });
      }

      this.scrollThrottleTimeout = null;
    }, 16); // 约60fps的节流
  };
}