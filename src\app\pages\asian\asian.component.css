mat-sidenav-container {
  height: 100vh;
  width: 100vw;
}

mat-sidenav {
  width: 180px;
  background-color: #f5f5f5;
  transform: translateZ(0); /* 启用硬件加速，防止跳动 */
  will-change: transform; /* 优化动画性能 */

  /* 隐藏滚动条 */
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* 隐藏 Webkit 浏览器的滚动条 */
mat-sidenav::-webkit-scrollbar {
  display: none;
}

/* 隐藏 mat-nav-list 的滚动条 */
mat-nav-list {
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

mat-nav-list::-webkit-scrollbar {
  display: none;
}

/* 通用滚动条隐藏 - 针对所有可能的滚动容器 */
mat-sidenav-container,
mat-sidenav-container *,
.category-grid {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

mat-sidenav-container::-webkit-scrollbar,
mat-sidenav-container *::-webkit-scrollbar,
.category-grid::-webkit-scrollbar {
  display: none;
}

mat-sidenav-content {
  padding: 20px 0 20px 20px; /* 不再为购物车预留底部空间 */
  height: 100vh;
  overflow: hidden;
}

/* 移动端菜单头部 */
.mobile-menu-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: flex-end;
}

/* 移动端菜单切换按钮 */
.mobile-menu-toggle {
  position: fixed;
  top: 16px;
  left: 16px;
  z-index: 1000;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

/* 分类方块网格容器 */
.category-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
  padding: 16px;
  width: 100%;
}

/* 单个分类方块样式 */
.category-block {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 110px; /* 增加高度以适应两行文本 */
  margin: 0 auto; /* 居中对齐 */
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  user-select: none;
  position: relative;
  overflow: hidden;
  will-change: transform, background-color, box-shadow;
  transform: translateZ(0); /* 启用硬件加速 */
}

/* 方块悬停效果 */
.category-block:hover {
  background-color: rgba(255, 255, 255, 0.95);
  transform: translateY(-2px) translateZ(0);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border-color: rgba(0, 0, 0, 0.12);
}

/* 激活状态的方块 */
.category-block.active-category {
  background-color: rgba(63, 81, 181, 0.1) !important;
  border-color: #3f51b5 !important;
  color: #3f51b5 !important;
  box-shadow: 0 4px 15px rgba(63, 81, 181, 0.2);
}

/* 激活状态的悬停效果 */
.category-block.active-category:hover {
  background-color: rgba(63, 81, 181, 0.15) !important;
  transform: translateY(-2px) translateZ(0);
  box-shadow: 0 8px 25px rgba(63, 81, 181, 0.25);
}

/* 分类图标样式 */
.category-icon {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-icon img {
  width: 28px;
  height: 28px;
  object-fit: cover;
  border-radius: 4px;
}

.category-icon mat-icon {
  font-size: 28px;
  width: 28px;
  height: 28px;
  color: inherit;
}

/* 分类文字样式 */
.category-text {
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
  color: inherit;
  max-width: 90px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  height: 28px; /* 约2行文本的高度 */
}

@media (max-width: 768px) {
  mat-sidenav {
    width: 80vw;
    /* 确保移动端也隐藏滚动条 */
    overflow-y: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  /* 移动端隐藏 Webkit 浏览器的滚动条 */
  mat-sidenav::-webkit-scrollbar {
    display: none;
  }

  /* 移动端隐藏 mat-nav-list 的滚动条 */
  mat-nav-list {
    overflow-y: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  mat-nav-list::-webkit-scrollbar {
    display: none;
  }

  mat-sidenav-content {
    padding: 60px 0 10px 10px; /* 移动端也不预留底部空间 */
  }

  /* 移动端方块网格调整 */
  .category-grid {
    grid-template-columns: 1fr;
    gap: 10px;
    padding: 12px;
  }

  /* 移动端方块尺寸调整 */
  .category-block {
    width: 110px;
    height: 100px; /* 增加移动端高度以适应两行文本 */
    border-radius: 10px;
  }

  /* 移动端图标和文字调整 */
  .category-icon img {
    width: 24px;
    height: 24px;
  }

  .category-icon mat-icon {
    font-size: 24px;
    width: 24px;
    height: 24px;
  }

  .category-text {
    font-size: 11px;
    max-width: 80px;
    height: 26px; /* 移动端稍微调整高度 */
  }
}


