import { Component, Inject, OnInit, ElementRef, ViewChild, AfterViewInit, OnDestroy } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatRadioModule } from '@angular/material/radio';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { Food } from '../../data/food.data';
import { DeliOrderingFoodItemClient, FoodItemDetailsDto } from '../../service/backoffice';
import { CartService } from '../../service/cart.service';

export interface FoodDetailDialogData {
  food: Food;
  storeId: number;
  editMode?: boolean; // 是否为编辑模式
  cartItem?: any; // 购物车商品信息（编辑模式时使用）
}

// 选择状态管理接口 - 兼容旧版本
export interface AttributeSelection {
  groupId: string;
  selectedItems: string[]; // 选中的item ID数组
}

export interface ProductConfiguration {
  quantity: number;
  selections: AttributeSelection[];
  totalPrice: number;
  // 商品基本信息
  name?: string;
  description?: string;
  image?: string;
  foodItemId?: string;
  basePrice?: number; // 商品基础价格
  // 选择的属性（用于购物车显示）
  selectedAttributes?: any[];
  // 备注信息
  memo?: string;
}

@Component({
  selector: 'app-food-detail-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatRadioModule,
    MatCheckboxModule,
    MatExpansionModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule
  ],
  templateUrl: './food-detail-dialog.component.html',
  styleUrls: ['./food-detail-dialog.component.scss']
})
export class FoodDetailDialogComponent implements OnInit, AfterViewInit, OnDestroy {
  isLoading = false;
  error: string | null = null;
  itemDetails: FoodItemDetailsDto | null = null;

  // Product configuration state
  productConfig: ProductConfiguration = {
    quantity: 1,
    selections: [],
    totalPrice: 0,
    memo: ''
  };

  // 缓存选择状态，避免频繁查找
  private selectionCache = new Map<string, string>(); // 单选缓存
  private multiSelectionCache = new Map<string, string[]>(); // 多选缓存

  // 验证错误
  validationErrors: string[] = [];
  currentImageIndex = 0;
  allImages: string[] = [];

  // 添加到购物车状态
  isAddingToCart = false;
  addToCartError: string | null = null;

  // 骨架屏控制
  skeletonGroupCount = 3; // 固定骨架屏组数量

  @ViewChild('imageContainer', { static: false }) imageContainer!: ElementRef;
  private hammerManager: any;
  isTransitioning = false;

  constructor(
    public dialogRef: MatDialogRef<FoodDetailDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: FoodDetailDialogData,
    private foodItemClient: DeliOrderingFoodItemClient,
    private cartService: CartService
  ) {}

  ngOnInit(): void {
    this.setupImages();
    this.loadItemDetails();
    this.initializeProductConfig();
  }

  private initializeProductConfig() {
    if (this.data.editMode && this.data.cartItem) {
      // 编辑模式：使用购物车中的数量和备注
      this.productConfig.quantity = this.data.cartItem.quantity;
      this.productConfig.memo = this.data.cartItem.memo || '';

      // 注意：不在这里调用initializeEditModeSelections，因为API数据还没有加载
      // 选择状态的初始化将在API数据加载完成后进行

      console.log('编辑模式基础配置初始化:', {
        quantity: this.productConfig.quantity,
        memo: this.productConfig.memo,
        说明: '选择状态将在API数据加载完成后初始化'
      });
    } else {
      // 添加模式：使用商品的基础价格
      this.productConfig.totalPrice = this.data.food.price;
      this.calculateTotalPrice();
    }
  }

  /**
   * 初始化编辑模式的选择状态
   */
  private initializeEditModeSelections() {
    if (!this.data.cartItem?.selectedAttributes || !this.itemDetails?.attributeGroup) return;

    console.log('=== 开始初始化编辑模式选择状态 ===');
    console.log('购物车商品的selectedAttributes:', this.data.cartItem.selectedAttributes);
    console.log('API返回的attributeGroup:', this.itemDetails.attributeGroup);

    // 清空现有选择和缓存
    this.productConfig.selections = [];
    this.selectionCache.clear();
    this.multiSelectionCache.clear();

    // 获取购物车中所有选中的属性项
    const selectedItems = this.data.cartItem.selectedAttributes.flatMap((attr: any) => attr.items || []);
    console.log('购物车中选中的所有属性项:', selectedItems);

    // 遍历API返回的属性组，找到匹配的选中项
    this.itemDetails.attributeGroup.forEach(group => {
      const groupId = group.id || '';
      if (!groupId) return; // 跳过没有ID的组

      const matchingItems = selectedItems.filter((selectedItem: any) => {
        // 在当前组中查找是否有匹配的项目
        return group.items?.some(groupItem => groupItem.id === selectedItem.id);
      });

      if (matchingItems.length > 0) {
        const selectedItemIds = matchingItems.map((item: any) => item.id);
        this.productConfig.selections.push({
          groupId: groupId,
          selectedItems: selectedItemIds
        });

        // 更新缓存
        if (selectedItemIds.length === 1) {
          // 单选组缓存
          this.selectionCache.set(groupId, selectedItemIds[0]);
        } else if (selectedItemIds.length > 1) {
          // 多选组缓存
          this.multiSelectionCache.set(groupId, selectedItemIds);
        }

        console.log(`组 ${groupId} 的选中项:`, selectedItemIds);
      }
    });

    console.log('最终初始化的选择状态:', this.productConfig.selections);
    console.log('=== 编辑模式选择状态初始化完成 ===');
  }

  ngAfterViewInit(): void {
    this.setupGestureHandling();
  }

  ngOnDestroy(): void {
    if (this.hammerManager) {
      this.hammerManager.destroy();
    }
  }

  private setupImages(): void {
    // 设置所有图片（主图 + 附图）
    this.allImages = [this.data.food.image];
    if (this.data.food.images && this.data.food.images.length > 0) {
      this.allImages.push(...this.data.food.images.map(img => img.imagePath));
    }

    // 预加载图片以减少闪烁
    this.preloadImages();
  }

  private preloadImages(): void {
    this.allImages.forEach(imageSrc => {
      if (imageSrc) {
        const img = new Image();
        img.onload = () => {
          // 图片加载完成，可以添加loaded类或其他处理
        };
        img.onerror = () => {
          console.warn('Failed to preload image:', imageSrc);
        };
        img.src = imageSrc;
      }
    });
  }

  private loadItemDetails(): void {
    // 从 Food 对象中获取 itemId (使用 food.id)
    const itemId = this.data.food.id;

    const storeId = 2; // 固定使用商店ID 2

    console.log('调用商品详情API参数:', {
      storeId: storeId,
      itemId: itemId,
      foodName: this.data.food.name
    });

    if (!itemId) {
      this.error = 'Missing product ID information';
      return;
    }

    this.isLoading = true;
    this.error = null;

    this.foodItemClient.getFoodItemDetails(storeId, itemId).subscribe({
      next: (response) => {
        console.log('=== API原始返回数据 ===');
        console.log(response.result);
        console.log('=== attributeGroup数据 ===');
        console.log(response.result?.attributeGroup);

        if (response.result) {
          this.itemDetails = response.result;

          // 在API数据加载完成后，重新初始化编辑模式的选择状态
          if (this.data.editMode && this.data.cartItem) {
            this.initializeEditModeSelections();
            this.calculateTotalPrice();
            console.log('API数据加载完成后重新初始化编辑模式选择:', this.productConfig.selections);
          }
        } else {
          this.error = 'No product details found';
        }

        // 瞬间切换，移除延迟
        this.isLoading = false;
      },
      error: (error) => {
        console.error('获取商品详情失败:', error);
        this.error = 'Failed to load product details';

        // 瞬间切换，移除延迟
        this.isLoading = false;
      }
    });
  }



  /**
   * 获取骨架屏组数组（用于ngFor）
   */
  getSkeletonGroups(): number[] {
    return Array(this.skeletonGroupCount).fill(0).map((_, i) => i);
  }

  onClose(): void {
    this.dialogRef.close();
  }

  onAddToCart(): void {
    // TODO: 实现添加到购物车功能
    console.log('Add to cart:', this.data.food);
    this.dialogRef.close({ action: 'add-to-cart', food: this.data.food });
  }

  // 图片轮播功能
  previousImage(): void {
    if (this.allImages.length > 1 && !this.isTransitioning) {
      this.isTransitioning = true;
      this.currentImageIndex = (this.currentImageIndex - 1 + this.allImages.length) % this.allImages.length;

      // 重置过渡状态
      setTimeout(() => {
        this.isTransitioning = false;
      }, 300);
    }
  }

  nextImage(): void {
    if (this.allImages.length > 1 && !this.isTransitioning) {
      this.isTransitioning = true;
      this.currentImageIndex = (this.currentImageIndex + 1) % this.allImages.length;

      // 重置过渡状态
      setTimeout(() => {
        this.isTransitioning = false;
      }, 300);
    }
  }

  getCurrentImage(): string {
    return this.allImages[this.currentImageIndex] || '/assets/images/placeholder-food.svg';
  }

  getTotalImages(): number {
    return this.allImages.length;
  }

  /**
   * 处理图片加载错误
   * @param event 错误事件
   */
  onImageError(event: any): void {
    console.warn('Image failed to load:', event.target.src);
    event.target.src = '/assets/images/placeholder-food.svg';
  }

  /**
   * 设置手势处理
   */
  private setupGestureHandling(): void {
    if (this.imageContainer && this.allImages.length > 1) {
      // 使用原生触摸事件代替HammerJS
      const element = this.imageContainer.nativeElement;
      let startX = 0;
      let startY = 0;
      let endX = 0;
      let endY = 0;

      element.addEventListener('touchstart', (e: TouchEvent) => {
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
      });

      element.addEventListener('touchend', (e: TouchEvent) => {
        endX = e.changedTouches[0].clientX;
        endY = e.changedTouches[0].clientY;
        this.handleSwipe(startX, startY, endX, endY);
      });
    }
  }

  /**
   * 处理滑动手势
   */
  private handleSwipe(startX: number, startY: number, endX: number, endY: number): void {
    const deltaX = endX - startX;
    const deltaY = endY - startY;
    const minSwipeDistance = 50;

    // 确保是水平滑动且距离足够
    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > minSwipeDistance) {
      if (deltaX > 0) {
        // 向右滑动 - 上一张
        this.onSwipeRight();
      } else {
        // 向左滑动 - 下一张
        this.onSwipeLeft();
      }
    }
  }

  /**
   * 处理向左滑动
   */
  private onSwipeLeft(): void {
    if (!this.isTransitioning && this.allImages.length > 1) {
      this.nextImage();
    }
  }

  /**
   * 处理向右滑动
   */
  private onSwipeRight(): void {
    if (!this.isTransitioning && this.allImages.length > 1) {
      this.previousImage();
    }
  }

  // ===== 规格选择相关方法 =====

  /**
   * 处理单选选择
   */
  onRadioSelection(groupId: string, itemId: string) {
    const existingSelection = this.productConfig.selections.find(s => s.groupId === groupId);
    if (existingSelection) {
      existingSelection.selectedItems = [itemId];
    } else {
      this.productConfig.selections.push({
        groupId: groupId,
        selectedItems: [itemId]
      });
    }

    // 更新缓存
    this.selectionCache.set(groupId, itemId);

    this.calculateTotalPrice();
    this.validateSelections();
  }

  /**
   * 处理下拉框选择变化
   */
  onSelectChange(groupId: string, event: any): void {
    const itemId = event.value;
    if (itemId === '') {
      // 选择了"None"，清除选择
      this.productConfig.selections = this.productConfig.selections.filter(s => s.groupId !== groupId);
      // 清除缓存
      this.selectionCache.delete(groupId);
    } else {
      this.onRadioSelection(groupId, itemId);
    }
  }

  /**
   * 处理多选下拉框选择变化
   */
  onMultiSelectChange(groupId: string, event: any): void {
    const selectedItems = event.value;
    const existingSelection = this.productConfig.selections.find(s => s.groupId === groupId);

    if (selectedItems && selectedItems.length > 0) {
      if (existingSelection) {
        existingSelection.selectedItems = selectedItems;
      } else {
        this.productConfig.selections.push({
          groupId,
          selectedItems: selectedItems
        });
      }
      // 更新多选缓存
      this.multiSelectionCache.set(groupId, selectedItems);
    } else {
      // 没有选择任何项目，移除这个组的选择
      this.productConfig.selections = this.productConfig.selections.filter(s => s.groupId !== groupId);
      // 清除多选缓存
      this.multiSelectionCache.delete(groupId);
    }

    this.calculateTotalPrice();
    this.validateSelections();
  }

  /**
   * 处理多选选择
   */
  onCheckboxSelection(groupId: string, itemId: string, checked: boolean) {
    let selection = this.productConfig.selections.find(s => s.groupId === groupId);
    if (!selection) {
      selection = { groupId: groupId, selectedItems: [] };
      this.productConfig.selections.push(selection);
    }

    if (checked) {
      // 检查是否超过最大选择数量
      const group = this.itemDetails?.attributeGroup?.find(g => g.id === groupId);
      if (group && group.maximumSelected && selection.selectedItems.length >= group.maximumSelected) {
        return; // 不允许超过最大选择数量
      }
      if (!selection.selectedItems.includes(itemId)) {
        selection.selectedItems.push(itemId);
      }
    } else {
      selection.selectedItems = selection.selectedItems.filter(id => id !== itemId);
    }

    // 更新多选缓存
    this.multiSelectionCache.set(groupId, selection.selectedItems);

    this.calculateTotalPrice();
    this.validateSelections();
  }

  /**
   * 检查选项是否被选中
   */
  isItemSelected(groupId: string, itemId: string): boolean {
    const selection = this.productConfig.selections.find(s => s.groupId === groupId);
    return selection ? selection.selectedItems.includes(itemId) : false;
  }

  /**
   * 获取单选组的选中值 - 优化版本，使用缓存减少频繁查找
   */
  getSelectedRadioValue(groupId: string): string {
    // 优先从缓存获取
    if (this.selectionCache.has(groupId)) {
      return this.selectionCache.get(groupId) || '';
    }

    // 缓存未命中时从selections查找并更新缓存
    const selection = this.productConfig.selections.find(s => s.groupId === groupId);
    const selectedValue = selection && selection.selectedItems.length > 0 ? selection.selectedItems[0] : '';
    this.selectionCache.set(groupId, selectedValue);
    return selectedValue;
  }

  /**
   * 检查属性组是否为必选
   */
  isGroupRequired(group: any): boolean {
    return group && group.minimumSelected > 0;
  }

  /**
   * 检查属性组是否已经被选择
   */
  isGroupSelected(group: any): boolean {
    if (!group || !group.id) return false;

    const groupId = group.id;
    const selection = this.productConfig.selections.find(s => s.groupId === groupId);

    if (!selection || !selection.selectedItems || selection.selectedItems.length === 0) {
      return false;
    }

    // 对于必选组，检查是否满足最小选择数量
    if (this.isGroupRequired(group)) {
      return selection.selectedItems.length >= group.minimumSelected;
    }

    // 对于可选组，只要有选择就算已选择
    return selection.selectedItems.length > 0;
  }

  /**
   * 检查属性组是否为单选
   */
  isGroupSingleSelection(group: any): boolean {
    return group && group.maximumSelected === 1;
  }

  /**
   * 检查属性组是否为多选
   */
  isGroupMultipleSelection(group: any): boolean {
    return group && group.maximumSelected > 1;
  }

  /**
   * 安全获取属性值
   */
  safeGetProperty<T>(obj: any, property: string, defaultValue: T): T {
    return obj && obj[property] !== undefined ? obj[property] : defaultValue;
  }

  // 触摸事件处理
  private touchStartX = 0;
  private touchStartY = 0;
  private minSwipeDistance = 50;

  /**
   * 触摸开始事件
   */
  onTouchStart(event: TouchEvent): void {
    this.touchStartX = event.touches[0].clientX;
    this.touchStartY = event.touches[0].clientY;
  }

  /**
   * 触摸移动事件
   */
  onTouchMove(event: TouchEvent): void {
    // 可以在这里添加滑动过程中的处理逻辑
    event.preventDefault();
  }

  /**
   * 触摸结束事件
   */
  onTouchEnd(event: TouchEvent): void {
    if (!event.changedTouches || event.changedTouches.length === 0) {
      return;
    }

    const touchEndX = event.changedTouches[0].clientX;
    const touchEndY = event.changedTouches[0].clientY;

    const deltaX = touchEndX - this.touchStartX;
    const deltaY = touchEndY - this.touchStartY;

    // 检查是否为水平滑动且距离足够
    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > this.minSwipeDistance) {
      if (deltaX > 0) {
        // 向右滑动 - 上一张图片
        this.previousImage();
      } else {
        // 向左滑动 - 下一张图片
        this.nextImage();
      }
    }
  }

  /**
   * 计算总价格（支持添加模式和编辑模式）
   */
  private calculateTotalPrice() {
    // 确定基础价格
    let basePrice: number;
    let mode: string;

    if (this.data.editMode && this.data.cartItem) {
      // 编辑模式：使用存储的原始基础价格
      basePrice = this.data.cartItem.basePrice;
      mode = '编辑模式';
    } else {
      // 添加模式：使用商品的基础价格
      basePrice = this.data.food.price;
      mode = '添加模式';
    }

    console.log(`=== ${mode}价格计算开始 ===`);
    console.log('基础价格:', basePrice);
    console.log('数量 (this.productConfig.quantity):', this.productConfig.quantity);

    // 计算基础总价
    let totalPrice = basePrice * this.productConfig.quantity;
    console.log('基础总价 (基础价格 × 数量):', totalPrice);

    let optionsTotal = 0;
    // 添加选项价格
    this.productConfig.selections.forEach(selection => {
      selection.selectedItems.forEach(itemId => {
        const group = this.itemDetails?.attributeGroup?.find(g => g.id === selection.groupId);
        const item = group?.items?.find(i => i.id === itemId);
        if (item && item.price) {
          const optionPrice = item.price * this.productConfig.quantity;
          optionsTotal += optionPrice;
          totalPrice += optionPrice;
          console.log(`选项 ${item.name}: 单价$${item.price} × ${this.productConfig.quantity} = $${optionPrice}`);
        }
      });
    });

    console.log('选项总价:', optionsTotal);
    console.log('最终总价:', totalPrice);
    console.log(`=== ${mode}价格计算结束 ===`);

    this.productConfig.totalPrice = totalPrice;
  }

  /**
   * 验证选择
   */
  private validateSelections() {
    this.validationErrors = [];

    if (!this.itemDetails?.attributeGroup) return;

    this.itemDetails.attributeGroup.forEach(group => {
      const selection = this.productConfig.selections.find(s => s.groupId === group.id);
      const selectedCount = selection ? selection.selectedItems.length : 0;

      if (group.minimumSelected && selectedCount < group.minimumSelected) {
        this.validationErrors.push(`Please select at least ${group.minimumSelected} option(s) for ${group.description || 'this group'}`);
      }

      if (group.maximumSelected && selectedCount > group.maximumSelected) {
        this.validationErrors.push(`Please select at most ${group.maximumSelected} option(s) for ${group.description || 'this group'}`);
      }
    });
  }

  /**
   * 数量增加
   */
  increaseQuantity() {
    this.productConfig.quantity++;
    this.calculateTotalPrice();
  }

  /**
   * 数量减少
   */
  decreaseQuantity() {
    if (this.productConfig.quantity > 1) {
      this.productConfig.quantity--;
      this.calculateTotalPrice();
    }
  }

  /**
   * 添加到购物车
   */
  addToCart() {
    this.validateSelections();

    if (this.validationErrors.length > 0) {
      return; // 有验证错误，不允许添加
    }

    this.isAddingToCart = true;
    this.addToCartError = null;

    const storeId = this.data.storeId;
    const foodItemId = this.data.food.id;

    console.log('开始创建购物车:', {
      storeId,
      foodItemId,
      productConfig: this.productConfig
    });

    // 设置商品基本信息到productConfig
    this.productConfig.name = this.data.food.name || this.data.food.description;
    this.productConfig.description = this.itemDetails?.item?.productDetails || this.data.food.description;
    this.productConfig.image = this.data.food.image;
    this.productConfig.foodItemId = foodItemId;

    // 设置基础价格
    if (this.data.editMode && this.data.cartItem) {
      // 编辑模式：直接使用存储的原始基础价格
      this.productConfig.basePrice = this.data.cartItem.basePrice;
      console.log('=== 编辑模式基础价格设置 ===');
      console.log('使用存储的原始基础价格:', this.data.cartItem.basePrice);
      console.log('购物车商品当前总价:', this.data.cartItem.price);
      console.log('购物车商品数量:', this.data.cartItem.quantity);
      console.log('购物车商品的选项:', this.data.cartItem.selectedAttributes);
      console.log('=== 基础价格设置完成 ===');
    } else {
      // 添加模式：使用商品的基础价格
      this.productConfig.basePrice = this.data.food.price;
      console.log('添加模式基础价格:', this.productConfig.basePrice);
    }

    // 转换选择的属性为购物车格式
    this.productConfig.selectedAttributes = this.convertSelectionsToAttributes();

    console.log('开始调用购物车API:', { storeId, productConfig: this.productConfig });

    // 立即关闭弹窗
    this.dialogRef.close({
      action: this.data.editMode ? 'item-updating' : 'item-adding-to-cart',
      productConfig: this.productConfig
    });

    if (this.data.editMode && this.data.cartItem) {
      // 编辑模式：更新购物车商品
      this.cartService.updateCartItem(storeId, this.data.cartItem.id, this.productConfig).subscribe({
        next: (response) => {
          console.log('=== 购物车商品更新成功 ===');
          console.log('返回信息:', response);
          // 成功时不做任何用户反馈
        },
        error: (error) => {
          console.error('购物车商品更新失败:', error);
          // 设置错误状态到购物车服务
          this.cartService.setError('Failed to update item');
        }
      });
    } else {
      // 添加模式：添加新商品到购物车
      this.cartService.addItemToCart(storeId, this.productConfig).subscribe({
        next: (response) => {
          console.log('=== 购物车操作成功 ===');
          console.log('返回信息:', response);
          // 成功时不做任何用户反馈
        },
        error: (error) => {
          console.error('购物车操作失败:', error);
          // 设置错误状态到购物车服务
          this.cartService.setError('Failed to add item');
        }
      });
    }
  }

  /**
   * 检查是否可以添加到购物车
   */
  canAddToCart(): boolean {
    return this.validationErrors.length === 0 && !this.isLoading && !this.isAddingToCart;
  }

  /**
   * 获取属性组中选中的项目ID列表 - 优化版本，使用缓存减少频繁查找
   */
  getSelectedItemsForGroup(groupId: string): string[] {
    // 优先从缓存获取
    if (this.multiSelectionCache.has(groupId)) {
      return this.multiSelectionCache.get(groupId) || [];
    }

    // 缓存未命中时从selections查找并更新缓存
    const selection = this.productConfig.selections.find(s => s.groupId === groupId);
    const selectedItems = selection ? selection.selectedItems : [];
    this.multiSelectionCache.set(groupId, selectedItems);
    return selectedItems;
  }

  /**
   * 根据项目ID获取项目名称
   */
  getItemName(group: any, itemId: string): string {
    const items = this.safeGetProperty(group, 'items', []);
    const item = items.find((item: any) => this.safeGetProperty(item, 'id', '') === itemId);
    return item ? this.safeGetProperty(item, 'name', 'Option') : 'Option';
  }

  /**
   * 根据项目ID获取项目价格
   */
  getItemPrice(group: any, itemId: string): number {
    const items = this.safeGetProperty(group, 'items', []);
    const item = items.find((item: any) => this.safeGetProperty(item, 'id', '') === itemId);
    return item ? this.safeGetProperty(item, 'price', 0) : 0;
  }

  /**
   * 获取格式化的属性组标题
   */
  getGroupTitle(group: any): string {
    if (!group) return 'Options';

    // 优先使用name字段
    const name = group.name || '';
    if (typeof name === 'string' && name.trim()) {
      return name;
    }

    // 其次使用title字段
    const title = group.title || '';
    if (typeof title === 'string' && title.trim()) {
      return title;
    }

    // 最后使用description字段，但进行格式化处理
    const description = group.description || '';
    if (typeof description === 'string' && description.trim()) {
      // 移除问号并格式化
      let formatted = description.replace(/\?$/, ''); // 移除末尾的问号

      // 如果以"Add "开头，将其转换为更合适的格式
      if (formatted.toLowerCase().startsWith('add ')) {
        formatted = formatted.substring(4); // 移除"Add "
      }

      // 首字母大写
      formatted = formatted.charAt(0).toUpperCase() + formatted.slice(1);

      return formatted;
    }

    // 默认值
    return 'Options';
  }

  /**
   * 转换选择的属性为购物车格式
   */
  private convertSelectionsToAttributes(): any[] {
    const attributes: any[] = [];

    console.log('开始转换选择的属性:', this.productConfig.selections);

    this.productConfig.selections.forEach(selection => {
      const group = this.itemDetails?.attributeGroup?.find(g => g.id === selection.groupId);
      if (group && selection.selectedItems.length > 0) {
        const items = selection.selectedItems.map(itemId => {
          const item = group.items?.find(i => i.id === itemId);
          const itemData = {
            id: itemId,
            name: item?.name || '',
            price: item?.price || 0 // 发送单价，让后端处理数量计算
          };
          console.log('选项详情:', {
            itemId: itemId,
            itemName: item?.name,
            itemPrice: item?.price,
            quantity: this.productConfig.quantity,
            说明: '发送单价，后端会处理数量计算'
          });
          return itemData;
        });

        const attributeGroup = {
          groupId: selection.groupId,
          groupName: group.description || '',
          items: items
        };

        console.log('转换的属性组:', attributeGroup);
        attributes.push(attributeGroup);
      }
    });

    console.log('最终转换的属性数组:', attributes);
    return attributes;
  }

  /**
   * 获取商品描述内容
   * 确保布局一致性，无论是否有描述都显示相同结构
   */
  getProductDescription(): string {
    if (this.itemDetails && this.itemDetails.item && this.itemDetails.item.productDetails) {
      return this.itemDetails.item.productDetails;
    }
    return ''; // 返回空字符串保持布局一致
  }

  /**
   * 判断是否有商品描述
   */
  hasProductDescription(): boolean {
    return !!(this.itemDetails && this.itemDetails.item && this.itemDetails.item.productDetails);
  }

  /**
   * 获取商品名称
   * 优先使用商品名称，如果为空则使用描述，最后使用默认值
   */
  getProductName(): string {
    if (this.data.food.name && this.data.food.name.trim()) {
      return this.data.food.name;
    }
    if (this.data.food.description && this.data.food.description.trim()) {
      return this.data.food.description;
    }
    return '商品详情';
  }

  /**
   * 判断选项是否被选中
   */
  isOptionSelected(groupId: string, itemId: string): boolean {
    const selection = this.productConfig.selections.find(s => s.groupId === groupId);
    if (!selection) {
      return false;
    }
    return selection.selectedItems.includes(itemId);
  }

  /**
   * 关闭特定组的下拉面板
   * 注意：这个方法主要用于多选下拉的Done按钮
   */
  closePanelForGroup(selectRef: any): void {
    // 直接调用mat-select的close方法来关闭面板
    if (selectRef && selectRef.close) {
      selectRef.close();
    }
  }

  /**
   * 获取触发器显示文本
   */
  getTriggerDisplayText(groupId: string): string {
    const selection = this.productConfig.selections.find(s => s.groupId === groupId);
    if (!selection || selection.selectedItems.length === 0) {
      return '';
    }

    const group = this.itemDetails?.attributeGroup?.find(g => g.id === groupId);
    if (!group) {
      return '';
    }

    if (selection.selectedItems.length === 1) {
      // 单选或只选了一个选项
      const item = group.items?.find(i => i.id === selection.selectedItems[0]);
      return item ? item.name || 'Option' : '';
    } else {
      // 多选
      return `${selection.selectedItems.length} selected`;
    }
  }

  /**
   * 判断组是否有选择
   */
  hasGroupSelection(groupId: string): boolean {
    const selection = this.productConfig.selections.find(s => s.groupId === groupId);
    return !!(selection && selection.selectedItems.length > 0);
  }

  /**
   * 获取选择状态的CSS类
   */
  getSelectCssClass(groupId: string): string {
    return this.hasGroupSelection(groupId) ? 'has-selection' : 'no-selection';
  }
}
