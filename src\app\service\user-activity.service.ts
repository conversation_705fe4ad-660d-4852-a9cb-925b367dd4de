import { Injectable, NgZone } from '@angular/core';
import { Router } from '@angular/router';
import { fromEvent, merge, Subscription } from 'rxjs';
import { throttleTime } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class UserActivityService {
  private readonly DEFAULT_TIMEOUT = 5 * 60 * 1000; // 5分钟
  
  private timeoutId: any = null;
  private activitySubscription: Subscription | null = null;
  private isActive = false;
  private isPaused = false;
  private timeoutDuration = this.DEFAULT_TIMEOUT;

  constructor(
    private router: Router,
    private ngZone: NgZone
  ) {}

  /**
   * 开始监听用户活动
   * @param timeoutMs 超时时间（毫秒），默认5分钟
   * @param redirectPath 超时后重定向的路径，默认'/home'
   */
  startMonitoring(timeoutMs: number = this.DEFAULT_TIMEOUT, redirectPath: string = '/home'): void {
    if (this.isActive) {
      this.stopMonitoring();
    }

    this.isActive = true;
    this.timeoutDuration = timeoutMs;
    
    console.log(`开始用户活动监听，超时时间: ${timeoutMs / 1000}秒`);

    // 监听各种用户活动事件
    const events = [
      'mousedown',
      'mousemove', 
      'keypress',
      'scroll',
      'touchstart',
      'touchmove',
      'click',
      'wheel'
    ];

    const eventStreams = events.map(event => 
      fromEvent(document, event)
    );

    // 合并所有事件流并节流处理
    this.activitySubscription = merge(...eventStreams)
      .pipe(throttleTime(1000)) // 1秒内只处理一次
      .subscribe(() => {
        if (!this.isPaused) {
          this.resetTimer(redirectPath);
        }
      });

    // 启动初始计时器
    this.resetTimer(redirectPath);
  }

  /**
   * 停止监听用户活动
   */
  stopMonitoring(): void {
    console.log('停止用户活动监听');
    
    this.isActive = false;
    this.isPaused = false;
    
    if (this.activitySubscription) {
      this.activitySubscription.unsubscribe();
      this.activitySubscription = null;
    }
    
    this.clearTimers();
  }

  /**
   * 暂停计时（例如在弹窗打开时）
   */
  pauseMonitoring(): void {
    console.log('暂停用户活动计时');
    this.isPaused = true;
    this.clearTimers();
  }

  /**
   * 恢复计时
   */
  resumeMonitoring(redirectPath: string = '/home'): void {
    if (this.isActive && this.isPaused) {
      console.log('恢复用户活动计时');
      this.isPaused = false;
      this.resetTimer(redirectPath);
    }
  }

  /**
   * 手动重置计时器
   */
  resetTimer(redirectPath: string = '/home'): void {
    if (!this.isActive || this.isPaused) {
      return;
    }

    this.clearTimers();

    // 设置超时计时器
    this.timeoutId = setTimeout(() => {
      if (this.isActive && !this.isPaused) {
        console.log('用户活动超时，返回首页');
        this.handleTimeout(redirectPath);
      }
    }, this.timeoutDuration);
  }

  /**
   * 获取当前是否正在监听
   */
  isMonitoring(): boolean {
    return this.isActive && !this.isPaused;
  }

  /**
   * 延长超时时间（例如购物车有商品时）
   */
  extendTimeout(additionalMs: number, redirectPath: string = '/home'): void {
    if (this.isActive && !this.isPaused) {
      console.log(`延长超时时间: ${additionalMs / 1000}秒`);
      this.timeoutDuration += additionalMs;
      this.resetTimer(redirectPath);
    }
  }

  /**
   * 处理超时事件
   */
  private handleTimeout(redirectPath: string): void {
    this.ngZone.run(() => {
      this.stopMonitoring();
      this.router.navigate([redirectPath]);
    });
  }

  /**
   * 清除所有计时器
   */
  private clearTimers(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
  }
}
