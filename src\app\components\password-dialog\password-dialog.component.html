<div class="password-dialog">
  <div class="dialog-header">
    <h2 mat-dialog-title>
      <mat-icon class="header-icon">security</mat-icon>
      {{ data.title || '管理员验证' }}
    </h2>
  </div>

  <div mat-dialog-content class="dialog-content">
    <p class="dialog-message">
      {{ data.message || '请输入管理员密码以继续' }}
    </p>
    
    <div class="password-hint">
      <mat-icon class="hint-icon">info</mat-icon>
      <span>提示：请输入今天的日期作为密码 (格式: YYYYMMDD)</span>
    </div>

    <mat-form-field appearance="outline" class="password-field">
      <mat-label>密码</mat-label>
      <input matInput
             [type]="hidePassword ? 'password' : 'text'"
             [formControl]="passwordControl"
             placeholder="请输入密码"
             (keydown.enter)="onEnterKey()"
             autocomplete="off">
      <button mat-icon-button 
              matSuffix 
              (click)="togglePasswordVisibility()" 
              type="button"
              [attr.aria-label]="'切换密码显示'"
              [attr.aria-pressed]="!hidePassword">
        <mat-icon>{{ hidePassword ? 'visibility' : 'visibility_off' }}</mat-icon>
      </button>
      <mat-error *ngIf="passwordControl.hasError('required')">
        密码不能为空
      </mat-error>
    </mat-form-field>

    <div class="error-message" *ngIf="errorMessage">
      <mat-icon class="error-icon">error</mat-icon>
      <span>{{ errorMessage }}</span>
    </div>
  </div>

  <div mat-dialog-actions class="dialog-actions">
    <button mat-button 
            (click)="onCancel()" 
            class="cancel-button">
      取消
    </button>
    <button mat-raised-button 
            color="primary" 
            (click)="validatePassword()"
            [disabled]="passwordControl.invalid"
            class="confirm-button">
      确认
    </button>
  </div>
</div>
