<div class="password-dialog">
  <div class="dialog-header">
    <h2 mat-dialog-title>
      <mat-icon class="header-icon">security</mat-icon>
      {{ data.title || 'Administrator Verification' }}
    </h2>
  </div>

  <div mat-dialog-content class="dialog-content">
    <p class="dialog-message">
      {{ data.message || 'Please enter administrator password to continue' }}
    </p>

    <mat-form-field appearance="outline" class="password-field">
      <mat-label>Password</mat-label>
      <input matInput
             [type]="hidePassword ? 'password' : 'text'"
             [formControl]="passwordControl"
             placeholder="Enter password"
             (keydown.enter)="onEnterKey()"
             autocomplete="off">
      <button mat-icon-button
              matSuffix
              (click)="togglePasswordVisibility()"
              type="button"
              [attr.aria-label]="'Toggle password visibility'"
              [attr.aria-pressed]="!hidePassword">
        <mat-icon>{{ hidePassword ? 'visibility' : 'visibility_off' }}</mat-icon>
      </button>
      <mat-error *ngIf="passwordControl.hasError('required')">
        Password is required
      </mat-error>
    </mat-form-field>

    <div class="error-message" *ngIf="errorMessage">
      <mat-icon class="error-icon">error</mat-icon>
      <span>{{ errorMessage }}</span>
    </div>
  </div>

  <div mat-dialog-actions class="dialog-actions">
    <button mat-button
            (click)="onCancel()"
            class="cancel-button">
      Cancel
    </button>
    <button mat-raised-button
            color="primary"
            (click)="validatePassword()"
            [disabled]="passwordControl.invalid"
            class="confirm-button">
      Confirm
    </button>
  </div>
</div>
