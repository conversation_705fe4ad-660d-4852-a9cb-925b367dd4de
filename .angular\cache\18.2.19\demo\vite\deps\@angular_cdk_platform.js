import {
  Platform,
  PlatformModule,
  RtlScrollAxisType,
  _getEventTarget,
  _getFocusedElementPierceShadowDom,
  _getShadowRoot,
  _isTestEnvironment,
  _supportsShadowDom,
  getRtlScrollAxisType,
  getSupportedInputTypes,
  normalizePassiveListenerOptions,
  supportsPassiveEventListeners,
  supportsScrollBehavior
} from "./chunk-RQZSDY3S.js";
import "./chunk-UND7GNPC.js";
import "./chunk-IGF3QYBZ.js";
import "./chunk-6Q4RANH6.js";
import "./chunk-FFZIAYYX.js";
import "./chunk-CXCX2JKZ.js";
export {
  Platform,
  PlatformModule,
  RtlScrollAxisType,
  _getEventTarget,
  _getFocusedElementPierceShadowDom,
  _getShadowRoot,
  _isTestEnvironment,
  _supportsShadowDom,
  getRtlScrollAxisType,
  getSupportedInputTypes,
  normalizePassiveListenerOptions,
  supportsPassiveEventListeners,
  supportsScrollBehavior
};
//# sourceMappingURL=@angular_cdk_platform.js.map
