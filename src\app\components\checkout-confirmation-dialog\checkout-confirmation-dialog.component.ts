import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { CartState, CartItem, AttributeSelection } from '../../service/cart.service';

export interface CheckoutConfirmationData {
  cartState: CartState;
  storeId: number;
}

@Component({
  selector: 'app-checkout-confirmation-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatDividerModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './checkout-confirmation-dialog.component.html',
  styleUrls: ['./checkout-confirmation-dialog.component.scss']
})
export class CheckoutConfirmationDialogComponent implements OnInit {
  isProcessing = false;

  constructor(
    public dialogRef: MatDialogRef<CheckoutConfirmationDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: CheckoutConfirmationData
  ) {}

  ngOnInit(): void {
    console.log('结算确认弹窗数据:', this.data);
  }

  /**
   * 获取购物车商品列表
   */
  getCartItems(): CartItem[] {
    return this.data.cartState.items || [];
  }

  /**
   * 获取商品总数量
   */
  getTotalQuantity(): number {
    return this.data.cartState.itemCount || 0;
  }

  /**
   * 获取小计金额
   */
  getSubtotal(): number {
    return this.data.cartState.totalAmount || 0;
  }

  /**
   * 获取税额
   */
  getTaxAmount(): number {
    return this.data.cartState.totalTaxAmount || 0;
  }

  /**
   * 获取总金额
   */
  getTotalAmount(): number {
    return this.getSubtotal() + this.getTaxAmount();
  }

  /**
   * 格式化价格显示
   */
  formatPrice(price: number): string {
    return `$${price.toFixed(2)}`;
  }

  /**
   * 获取商品属性显示文本
   */
  getAttributesText(item: CartItem): string {
    if (!item.selectedAttributes || item.selectedAttributes.length === 0) {
      return 'No specifications';
    }

    const attributeTexts: string[] = [];
    item.selectedAttributes.forEach((attr: AttributeSelection) => {
      if (attr.items && attr.items.length > 0) {
        const itemNames = attr.items.map(item => item.name).join(', ');
        attributeTexts.push(`${attr.groupName}: ${itemNames}`);
      }
    });

    return attributeTexts.length > 0 ? attributeTexts.join('; ') : 'No specifications';
  }

  /**
   * 获取商品属性标签列表
   */
  getAttributeTags(item: CartItem): Array<{name: string, price: number}> {
    const tags: Array<{name: string, price: number}> = [];
    
    if (item.selectedAttributes) {
      item.selectedAttributes.forEach((attr: AttributeSelection) => {
        if (attr.items) {
          attr.items.forEach(attrItem => {
            if (attrItem.price && attrItem.price > 0) {
              tags.push({
                name: attrItem.name,
                price: attrItem.price
              });
            }
          });
        }
      });
    }

    return tags;
  }

  /**
   * 取消结算
   */
  onCancel(): void {
    this.dialogRef.close({ action: 'cancel' });
  }

  /**
   * 确认结算
   */
  onConfirm(): void {
    this.isProcessing = true;
    this.dialogRef.close({ 
      action: 'confirm',
      storeId: this.data.storeId,
      cartState: this.data.cartState
    });
  }

  /**
   * 获取商品图片URL
   */
  getItemImageUrl(item: CartItem): string {
    return item.image || '/assets/images/placeholder-food.svg';
  }

  /**
   * 处理图片加载错误
   */
  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    img.src = '/assets/images/placeholder-food.svg';
  }

  /**
   * TrackBy函数用于优化列表渲染
   */
  trackByItemId(index: number, item: CartItem): string {
    return item.id;
  }
}
