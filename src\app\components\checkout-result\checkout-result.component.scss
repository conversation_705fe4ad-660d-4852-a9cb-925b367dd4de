.checkout-result-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-container,
.error-container {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  width: 100%;

  .loading-icon,
  .error-icon {
    font-size: 64px;
    width: 64px;
    height: 64px;
    margin-bottom: 20px;
  }

  .loading-icon {
    color: #2563eb;
    animation: pulse 2s infinite;
  }

  .error-icon {
    color: #ef4444;
  }

  h2 {
    margin: 0 0 16px 0;
    color: #1f2937;
  }

  p {
    color: #6b7280;
    margin: 8px 0;
  }

}

.success-container {
  max-width: 800px;
  width: 100%;
}

.success-header {
  text-align: center;
  margin-bottom: 32px;

  .success-icon {
    font-size: 80px;
    width: 80px;
    height: 80px;
    color: #10b981;
    margin-bottom: 16px;
  }

  h1 {
    margin: 0 0 12px 0;
    color: #1f2937;
    font-size: 2.5rem;
    font-weight: 700;
  }

  .success-message {
    color: #6b7280;
    font-size: 1.1rem;
    margin: 0;
  }
}

.order-info-card {
  margin-bottom: 32px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

  mat-card-header {
    background: #f8fafc;
    border-radius: 12px 12px 0 0;
    padding: 20px 24px;

    mat-card-title {
      color: #1f2937;
      font-size: 1.5rem;
      font-weight: 600;
      margin: 0;
    }
  }

  mat-card-content {
    padding: 24px;
  }
}

.order-summary {
  margin-bottom: 24px;

  .info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f3f4f6;

    &:last-child {
      border-bottom: none;
    }

    .label {
      font-weight: 500;
      color: #6b7280;
    }

    .value {
      font-weight: 600;
      color: #1f2937;
    }
  }
}

.products-section {
  margin: 24px 0;

  h3 {
    margin: 0 0 16px 0;
    color: #1f2937;
    font-size: 1.25rem;
    font-weight: 600;
  }
}

.product-list {
  .product-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 16px 0;
    border-bottom: 1px solid #f3f4f6;

    &:last-child {
      border-bottom: none;
    }

    .product-info {
      flex: 1;

      .product-name {
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 4px;
      }

      .product-specs {
        font-size: 0.875rem;
        color: #6b7280;
        margin-bottom: 8px;
      }

      .product-attributes {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;

        .attribute-tag {
          background: #e0f2fe;
          color: #0369a1;
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 0.75rem;
          font-weight: 500;
        }
      }
    }

    .product-pricing {
      text-align: right;
      min-width: 80px;

      .quantity {
        font-size: 0.875rem;
        color: #6b7280;
        margin-bottom: 4px;
      }

      .price {
        font-weight: 600;
        color: #2563eb;
        font-size: 1.1rem;
      }
    }
  }
}

.price-summary {
  margin-top: 24px;

  .price-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;

    .label {
      font-weight: 500;
      color: #6b7280;
    }

    .value {
      font-weight: 600;
      color: #1f2937;
    }

    &.total-row {
      border-top: 2px solid #e5e7eb;
      padding-top: 16px;
      margin-top: 8px;

      .label,
      .value {
        font-size: 1.25rem;
        font-weight: 700;
      }

      .value {
        color: #2563eb;
      }
    }
  }
}

.action-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;

  button {
    min-width: 160px;
    height: 48px;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 8px;

    mat-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .checkout-result-container {
    padding: 16px;
  }

  .success-header {
    h1 {
      font-size: 2rem;
    }

    .success-message {
      font-size: 1rem;
    }
  }

  .order-info-card {
    mat-card-content {
      padding: 16px;
    }
  }

  .product-item {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 12px;

    .product-pricing {
      text-align: left !important;
      min-width: auto !important;
    }
  }

  .action-buttons {
    flex-direction: column;

    button {
      width: 100%;
    }
  }
}

// 动画效果
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.success-container {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
