.category-container {
  height: 100%;
  overflow: hidden;
  scroll-behavior: smooth;
  will-change: transform;
  -webkit-overflow-scrolling: touch;
  contain: content;
}

.food-sections {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
  overscroll-behavior: contain;
  transform: translateZ(0);
  backface-visibility: hidden;
  contain: layout style;
}

.category-section {
  margin-bottom: 20px;
  contain: layout style paint;
  transform: translateZ(0);
  scroll-margin-top: 84px;
}

/* 分类标题样式 */
.category-header {
  margin-bottom: 20px;
}

.category-title {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  text-align: left;
  padding-left: 4px;
}

.food-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, 300px);
  gap: 20px;
  margin-bottom: 40px;
  justify-content: center;
  contain: layout style;
  will-change: transform;
}

mat-card {
  width: 300px;
  height: 380px; /* 稍微增加高度以适应新布局 */
  contain: content;
  transform: translateZ(0);
  will-change: transform;
  display: flex;
  flex-direction: column;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.food-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

/* 可点击内容区域样式 */
.clickable-content {
  cursor: pointer;
  transition: background-color 0.2s ease;
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
  min-height: 140px; /* 确保最小高度 */
  justify-content: flex-start; /* 统一使用顶部对齐 */
}

.clickable-content:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

/* 商品标题样式 */
.clickable-content h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 价格样式 */
.clickable-content .price {
  font-size: 1rem;
  font-weight: 700;
  color: #2563eb;
  margin: 0 0 8px 0;
}

/* 描述样式 - 固定高度确保布局一致 */
.clickable-content .description {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  height: 60px; /* 固定高度，约3行文本 */
  flex-shrink: 0; /* 防止高度被压缩 */
}



.food-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 卡片内容布局 */
mat-card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px !important;
}

mat-card-content h3 {
  margin: 0 0 8px 0;
  font-size: 1.1rem;
  font-weight: 500;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

mat-card-content p {
  margin: 0 0 8px 0;
  font-size: 0.9rem;
  line-height: 1.4;
  color: #666;
}

mat-card-content p.price {
  margin-top: auto;
  margin-bottom: -15px;
  font-size: 1.2rem;
  font-weight: 600;
  color: #2196F3;
}

mat-card-actions {
  padding: 0 16px 5px !important;
  margin: 0;
}

mat-card img {
  height: 200px;
  object-fit: cover;
  width: 100%;
  will-change: transform;
  transform: translateZ(0);
  content-visibility: auto;
  background: #f5f5f5;
  contain: strict;
}





@media (max-width: 768px) {
  .food-sections {
    padding: 10px;
  }

  .category-section {
    scroll-margin-top: 74px;
  }

  .category-title {
    font-size: 1.5rem;
    padding-left: 2px;
  }

  .category-header {
    margin-bottom: 15px;
  }

  .food-grid {
    grid-template-columns: repeat(auto-fit, 280px);
    justify-content: center;
  }

  mat-card {
    width: 280px;
    height: 380px;
  }

  .image-slider {
    height: 180px;
  }
}

.loading-message {
  padding: 20px;
  text-align: center;
  color: #666;
  font-style: italic;
  grid-column: 1 / -1; /* 占据整个网格宽度 */
}

/* 商品图片容器样式 */
.food-image-container {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
  border-radius: 12px 12px 0 0; /* 只有顶部圆角 */
  cursor: pointer;
}

.food-image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  transition: transform 0.3s ease;
}

.food-image-container:hover img {
  transform: scale(1.05);
}

/* 图片加载状态 */
.image-loading {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

