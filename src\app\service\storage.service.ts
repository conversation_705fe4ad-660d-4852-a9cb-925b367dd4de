import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class StorageService {
  private readonly STORE_ID_KEY = 'restaurant_store_id';

  constructor() {}

  /**
   * 获取存储的storeId
   * @returns storeId字符串，如果不存在则返回空字符串
   */
  getStoreId(): string {
    try {
      return localStorage.getItem(this.STORE_ID_KEY) || '';
    } catch (error) {
      console.error('获取storeId失败:', error);
      return '';
    }
  }

  /**
   * 保存storeId到本地存储
   * @param storeId 要保存的storeId
   * @returns 是否保存成功
   */
  setStoreId(storeId: string): boolean {
    try {
      if (storeId.trim()) {
        localStorage.setItem(this.STORE_ID_KEY, storeId.trim());
      } else {
        localStorage.removeItem(this.STORE_ID_KEY);
      }
      return true;
    } catch (error) {
      console.error('保存storeId失败:', error);
      return false;
    }
  }

  /**
   * 删除存储的storeId
   * @returns 是否删除成功
   */
  removeStoreId(): boolean {
    try {
      localStorage.removeItem(this.STORE_ID_KEY);
      return true;
    } catch (error) {
      console.error('删除storeId失败:', error);
      return false;
    }
  }

  /**
   * 检查是否已设置storeId
   * @returns 是否已设置storeId
   */
  hasStoreId(): boolean {
    return this.getStoreId().length > 0;
  }

  /**
   * 获取当前日期作为密码格式 (MMDDYYYY)
   * @returns 格式化的日期字符串
   */
  getCurrentDatePassword(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `${month}${day}${year}`;
  }

  /**
   * 验证密码是否为当前日期
   * @param password 输入的密码
   * @returns 是否验证通过
   */
  validateDatePassword(password: string): boolean {
    const currentDatePassword = this.getCurrentDatePassword();
    return password.trim() === currentDatePassword;
  }
}
