import { Injectable } from '@angular/core';
import { Observable, map, catchError, of } from 'rxjs';
import { Category } from '../data/food.data';
import { DeliOrderingFoodCategoryClient, FoodCategoryDto } from './backoffice';

@Injectable({
  providedIn: 'root'
})
export class CategoryService {
  constructor(private categoryClient: DeliOrderingFoodCategoryClient) {}

  /**
   * 获取指定商店的分类数据
   * @param storeId 商店ID
   * @returns Observable<Category[]>
   */
  getCategoriesForStore(storeId: number): Observable<Category[]> {
    return this.categoryClient.getCategorys(storeId).pipe(
      map(response => {
        console.log('分类API返回:', response.result);
        return response.result ? this.convertFoodCategoryDtosToCategories(response.result) : [];
      }),
      catchError(error => {
        console.error('获取分类数据失败:', error);
        return of([]); // 返回空数组作为fallback
      })
    );
  }

  /**
   * 将API返回的分类数据转换为组件使用的格式
   * @param foodCategories API返回的分类数据
   * @returns Category[] 转换后的分类数据
   */
  private convertFoodCategoryDtosToCategories(foodCategories: FoodCategoryDto[]): Category[] {
    return foodCategories.map(category => ({
      id: category.id || '', // 保留分类ID
      name: category.name?.trim() || '未知分类', // 去除名称中的空格
      route: this.generateRouteFromName(category.name?.trim() || '未知分类'),
      imagePath: this.ensureHttpsUrl(category.imagePath) // 保留分类图片路径
    }));
  }

  /**
   * 确保URL使用HTTPS协议
   * @param url 原始URL
   * @returns string 处理后的URL
   */
  private ensureHttpsUrl(url?: string): string | undefined {
    if (!url) return undefined;

    // 如果是相对路径，添加基础URL
    if (url.startsWith('/')) {
      return 'https://connect.syncmcr.com' + url;
    }

    // 如果是HTTP，转换为HTTPS
    if (url.startsWith('http://')) {
      return url.replace('http://', 'https://');
    }

    // 如果已经是HTTPS或其他协议，直接返回
    return url;
  }

  /**
   * 根据分类名称生成路由
   * @param name 分类名称
   * @returns string 路由字符串
   */
  private generateRouteFromName(name: string): string {
    // 将分类名称转换为路由格式
    // 例如: "Asian Cuisine" -> "/asian-cuisine"
    return '/' + name.toLowerCase()
      .replace(/\s+/g, '-')  // 将空格替换为连字符
      .replace(/[^a-z0-9-]/g, ''); // 移除特殊字符
  }
}
