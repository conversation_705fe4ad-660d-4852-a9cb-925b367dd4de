.cart-fab {
  position: fixed;
  bottom: 24px;
  right: 40px; // 增加右边距，从24px改为40px
  z-index: 1000;

  // 增大按钮尺寸
  width: 64px;
  height: 64px;
  
  // 现代美学设计
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  box-shadow: 
    0 8px 25px -5px rgba(37, 99, 235, 0.3),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  // 悬停效果
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 
      0 12px 35px -5px rgba(37, 99, 235, 0.4),
      0 8px 15px -3px rgba(0, 0, 0, 0.1);
  }
  
  &:active {
    transform: translateY(0) scale(0.98);
  }
  
  // 图标样式
  mat-icon {
    color: white;
    font-size: 28px; // 增大图标尺寸
    width: 28px;
    height: 28px;

    // 同步状态动画
    &.syncing {
      animation: spin 1s linear infinite;
    }
  }
  
  // 徽章自定义样式
  ::ng-deep .mat-badge-content {
    background: #ef4444;
    color: white;
    font-weight: 600;
    font-size: 12px;
    min-width: 20px;
    height: 20px;
    line-height: 20px;
    border-radius: 10px;
    border: 2px solid white;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .cart-fab {
    bottom: 20px;
    right: 32px; // 移动端也增加右边距

    // 移动端也增大一些
    width: 56px;
    height: 56px;

    mat-icon {
      font-size: 24px; // 移动端图标也稍大一些
      width: 24px;
      height: 24px;
    }
  }
}

// 动画效果
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

// 当有新商品添加时的动画
.cart-fab.animate-bounce {
  animation: bounce 1s ease-in-out;
}

// 同步状态旋转动画
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
