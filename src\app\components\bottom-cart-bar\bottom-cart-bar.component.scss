.bottom-cart-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;

  // 展开的商品列表面板
  .cart-items-panel {
    background: white;
    border-top: 1px solid #e5e7eb;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &.expanded {
      max-height: 300px; // 最大高度
    }

    .items-list {
      padding: 16px;
      display: flex;
      gap: 12px;
      overflow-x: auto;
      overflow-y: hidden;

      // 自定义滚动条
      &::-webkit-scrollbar {
        height: 4px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f5f9;
        border-radius: 2px;
      }

      &::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 2px;

        &:hover {
          background: #94a3b8;
        }
      }
    }

    .cart-item {
      flex-shrink: 0;
      width: 160px;
      background: white;
      border: 1px solid #e2e8f0;
      border-radius: 12px;
      padding: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      transition: all 0.2s ease;
      display: flex;
      flex-direction: column;
      min-height: 180px;

      .item-content {
        cursor: pointer;
        flex: 1;
        display: flex;
        flex-direction: column;
        transition: background-color 0.2s ease;
        border-radius: 8px;
        padding: 4px;
        margin: -4px;

        &:hover {
          background: #f8fafc;
        }

        &:active {
          background: #f1f5f9;
        }
      }

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }

      .item-image {
        width: 100%;
        height: 80px;
        border-radius: 8px;
        overflow: hidden;
        background: #f8fafc;
        margin-bottom: 8px;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .item-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        margin-bottom: 8px;
        min-height: 60px;

        .item-name {
          font-size: 0.875rem;
          font-weight: 600;
          color: #1f2937;
          line-height: 1.2;
          margin-bottom: 4px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .item-specs {
          font-size: 0.75rem;
          color: #64748b;
          line-height: 1.2;
          margin: 2px 0 4px 0;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }


        .item-price {
          font-size: 0.875rem;
          font-weight: 600;
          color: #2563eb;
          margin-top: auto;
        }
      }

      .item-controls {
        display: flex;
        align-items: center;
        gap: 3px;
        height: 28px;
        margin-top: auto;
        min-width: 0;
        flex-shrink: 0;
      }

      // 优化的数量控制器 - 底部购物车栏版本
      .quantity-controls {
        display: flex;
        align-items: center;
        gap: 0;
        background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        overflow: hidden;
        flex-shrink: 0;
        height: 28px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        &:hover {
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
          border-color: #cbd5e1;
        }

        .quantity-btn {
          width: 28px;
          height: 28px;
          min-width: 28px;
          border: none;
          background: transparent;
          color: #64748b;
          font-size: 14px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;

          &:hover:not(:disabled) {
            background: linear-gradient(145deg, #f1f5f9 0%, #e2e8f0 100%);
            color: #475569;
            transform: scale(1.05);
          }

          &:active:not(:disabled) {
            background: linear-gradient(145deg, #e2e8f0 0%, #cbd5e1 100%);
            transform: scale(0.95);
          }

          &:disabled {
            color: #cbd5e1;
            cursor: not-allowed;
            transform: none;
          }
        }

        .quantity {
          font-size: 0.8125rem;
          font-weight: 700;
          min-width: 28px;
          text-align: center;
          color: #1e293b;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 28px;
          background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
          border-left: 1px solid #e2e8f0;
          border-right: 1px solid #e2e8f0;
          transition: all 0.3s ease;

          // 数量变化动画
          &.quantity-changed {
            animation: quantityPulse 0.4s ease-out;
          }
        }
      }

    }
  }
}

.bottom-cart-bar {
  background: white;
  border-top: 1px solid #e5e7eb;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);

  // 同步进度条
  .sync-progress {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    z-index: 1;
  }

  .cart-bar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 16px;
    min-height: 52px;
    gap: 16px;

    .cart-info {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 12px;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 8px;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: #f9fafb;
      }

      .cart-summary {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 12px;

        .item-count {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 14px;
          color: #6b7280;

          .cart-icon {
            font-size: 20px;
            width: 20px;
            height: 20px;
            color: #2563eb;

            &.syncing {
              animation: spin 1s linear infinite;
            }
          }

          .count-text {
            font-weight: 500;
          }
        }

        .total-price {
          display: flex;
          align-items: center;
          gap: 8px;

          .amount {
            font-size: 18px;
            font-weight: 700;
            color: #2563eb;
          }

          .tax-amount {
            font-size: 12px;
            font-weight: 500;
            color: #6b7280;
          }
        }
      }

      .expand-arrow {
        .mat-icon {
          font-size: 24px;
          width: 24px;
          height: 24px;
          color: #6b7280;
          transition: transform 0.3s ease;

          &.rotated {
            transform: rotate(180deg);
          }
        }
      }
    }

    .checkout-section {
      .checkout-btn {
        min-width: 120px;
        height: 44px;
        font-size: 16px;
        font-weight: 600;
        border-radius: 8px;
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
        transition: all 0.2s ease;

        &:hover:not(:disabled) {
          transform: translateY(-1px);
          box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4);
        }

        &:active:not(:disabled) {
          transform: translateY(0);
        }

        &:disabled {
          background: #9ca3af;
          box-shadow: none;
          cursor: not-allowed;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .bottom-cart-container {
    .cart-items-panel {
      .items-list {
        padding: 12px;
        gap: 8px;
      }

      .cart-item {
        width: 140px;
        padding: 10px;

        .item-image {
          height: 70px;
          margin-bottom: 6px;
        }

        .item-info {
          margin-bottom: 6px;

          .item-name {
            font-size: 0.8rem;
          }

          .item-specs {
            font-size: 0.7rem;
          }

          .item-price {
            font-size: 0.8rem;
          }
        }

        .item-controls {
          gap: 3px;
          height: 24px; // 移动端固定高度
          min-width: 0; /* 允许收缩 */
        }

        .quantity-controls {
          height: 24px; // 移动端固定高度
          min-width: 72px; /* 最小宽度：24px * 3 */

          .quantity-btn {
            width: 24px;
            height: 24px;
            min-width: 24px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 1;
          }

          .quantity {
            min-width: 24px;
            height: 24px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 1;
          }
        }

        .edit-btn {
          width: 20px;
          height: 20px;
          min-width: 20px;
          border-radius: 3px;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0;
          background: white;
          border: 1px solid #e2e8f0;
          cursor: pointer;

          &:hover {
            background-color: rgba(33, 150, 243, 0.1);
            border-color: #2196F3;
          }

          .edit-icon {
            width: 10px;
            height: 10px;
            opacity: 0.7;
          }

          &:hover .edit-icon {
            opacity: 1;
          }
        }

        .remove-btn {
          width: 20px;
          height: 20px;
          min-width: 20px;
          border-radius: 3px;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0;

          .delete-icon {
            width: 10px;
            height: 10px;
            display: block;
          }
        }
      }
    }
  }

  .bottom-cart-bar {
    .cart-bar-content {
      padding: 10px 12px;
      min-height: 56px;
      gap: 12px;

      .cart-info {
        padding: 8px;

        .cart-summary {
          .item-count {
            font-size: 13px;

            .cart-icon {
              font-size: 18px;
              width: 18px;
              height: 18px;
            }
          }

          .total-price {
            .amount {
              font-size: 16px;
            }

            .tax-amount {
              font-size: 11px;
            }
          }
        }

        .expand-arrow .mat-icon {
          font-size: 22px;
          width: 22px;
          height: 22px;
        }
      }

      .checkout-section {
        .checkout-btn {
          min-width: 100px;
          height: 40px;
          font-size: 14px;
        }
      }
    }
  }
}

// 动画效果
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 进入和离开动画
.bottom-cart-bar {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

// 错误信息横幅
.error-banner {
  position: absolute;
  top: -40px;
  left: 0;
  right: 0;
  background: #fee2e2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 8px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  z-index: 2;

  .error-close-btn {
    background: none;
    border: none;
    color: #dc2626;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    padding: 0;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #fecaca;
    }
  }
}
