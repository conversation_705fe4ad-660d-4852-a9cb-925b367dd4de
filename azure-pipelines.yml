trigger:
- main

parameters:
- name: Environment
  displayName: Run Environment
  default: Staging
  values:
  - Staging
  - Production

variables:
  npm_config_cache: $(Pipeline.Workspace)/.npm
  staticAppsClientImage: mcr.microsoft.com/appsvc/staticappsclient:stable
  ${{ if eq(parameters.Environment, 'Production') }}:
    configuration: production
  ${{ if eq(parameters.Environment, 'Staging') }}:
    configuration: staging

stages:
- stage: Build
  displayName: Build and push stage
  jobs:
  - job: Build
    displayName: Build
    pool:
      name: Default
      demands: Agent.OS -equals Linux
    steps:
    - task: Cache@2
      condition: eq(variables.selfHosted, false)
      inputs:
        key: 'npm | "$(Agent.OS)" | Tekweld-Machine-Monitoring-Frontend/package-lock.json'
        restoreKeys: |
            npm | "$(Agent.OS)"
        path: $(npm_config_cache)
      displayName: Cache npm

    - task: NodeTool@0
      inputs:
        versionSpec: '22.x'
      displayName: 'Install Node.js'

    - script: |
        npm ci
        npm i --location=global @angular/cli@18.1.0
        npm install
        ng build --configuration $(configuration) --output-path $(Build.ArtifactStagingDirectory)
      displayName: 'npm install and build'
      
    - publish: $(Build.ArtifactStagingDirectory)
      artifact: drop

- stage: Deploy
  displayName: Deploy stage
  dependsOn: Build
  jobs:
  - deployment: Deploy
    displayName: Deploy
    pool:
      vmImage: ubuntu-latest
    environment: AzureStaticWebApps
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureStaticWebApp@0
            inputs:
              cwd: $(Pipeline.Workspace)
              app_location: 'drop/browser'
              ${{ if eq(parameters.Environment, 'Production') }}:
                azure_static_web_apps_api_token: '$(deployment_token)'
              ${{ if eq(parameters.Environment, 'Staging') }}:
                azure_static_web_apps_api_token: '$(staging_deployment_token)'
