<div class="checkout-result-container">
  <!-- 加载状态 -->
  <div *ngIf="loading" class="loading-container">
    <mat-icon class="loading-icon">hourglass_empty</mat-icon>
    <p>Processing your order...</p>
  </div>

  <!-- 错误状态 -->
  <div *ngIf="error && !loading" class="error-container">
    <mat-icon class="error-icon">error</mat-icon>
    <h2>Order Not Found</h2>
    <p>{{ error }}</p>
    <p>Redirecting to home page...</p>
  </div>

  <!-- 成功状态 -->
  <div *ngIf="orderResult && !loading" class="success-container">
    <!-- 成功图标和标题 -->
    <div class="success-header">
      <mat-icon class="success-icon">check_circle</mat-icon>
      <h1>Order Successful!</h1>
      <p class="success-message">Thank you for your order. Your payment has been processed successfully.</p>
    </div>

    <!-- 订单信息卡片 -->
    <mat-card class="order-info-card">
      <mat-card-header>
        <mat-card-title>Order Details</mat-card-title>
      </mat-card-header>
      
      <mat-card-content>
        <div class="order-summary">
          <div class="info-row">
            <span class="label">Transaction ID:</span>
            <span class="value">{{ orderResult.transactionID }}</span>
          </div>
          
          <div class="info-row">
            <span class="label">Order Time:</span>
            <span class="value">{{ formatTransactionDate() }}</span>
          </div>
          
          <div class="info-row">
            <span class="label">Total Items:</span>
            <span class="value">{{ orderResult.totalQuantity }}</span>
          </div>
        </div>

        <mat-divider></mat-divider>

        <!-- 商品列表 -->
        <div class="products-section">
          <h3>Items Ordered</h3>
          <div class="product-list">
            <div *ngFor="let product of orderResult.products" class="product-item">
              <div class="product-info">
                <div class="product-name">{{ product.description }}</div>
                <div class="product-specs" *ngIf="getAttributesText(product) !== '无规格'">
                  {{ getAttributesText(product) }}
                </div>
                <div class="product-attributes" *ngIf="getAttributesDetails(product).length > 0">
                  <span *ngFor="let attr of getAttributesDetails(product)" class="attribute-tag">
                    {{ attr.name }} (+{{ formatPrice(attr.price) }})
                  </span>
                </div>
              </div>
              <div class="product-pricing">
                <div class="quantity">Qty: {{ product.quantity }}</div>
                <div class="price">{{ formatPrice(product.price) }}</div>
              </div>
            </div>
          </div>
        </div>

        <mat-divider></mat-divider>

        <!-- 价格汇总 -->
        <div class="price-summary">
          <div class="price-row">
            <span class="label">Subtotal:</span>
            <span class="value">{{ formatPrice(orderResult.subtotal) }}</span>
          </div>
          
          <div class="price-row">
            <span class="label">Tax:</span>
            <span class="value">{{ formatPrice(orderResult.totalTaxAmount) }}</span>
          </div>
          
          <div class="price-row total-row">
            <span class="label">Total:</span>
            <span class="value">{{ formatPrice(orderResult.totalAmount) }}</span>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <button mat-raised-button color="primary" (click)="continueShopping()">
        <mat-icon>shopping_cart</mat-icon>
        Continue Shopping
      </button>
      
      <button mat-stroked-button (click)="goHome()">
        <mat-icon>home</mat-icon>
        Back to Home
      </button>
    </div>
  </div>
</div>
