<!-- 购物车侧边栏遮罩 -->
<div class="cart-sidebar-overlay" 
     [class.open]="isOpen" 
     (click)="closeSidebar()"></div>

<!-- 购物车侧边栏 -->
<div class="cart-sidebar" [class.open]="isOpen">
  <!-- 头部 -->
  <div class="cart-header">
    <h2>Products in the cart</h2>
    <div class="cart-total">
      <span class="total-label">Total</span>
      <span class="total-amount">${{ getTotalAmount().toFixed(2) }}</span>
      <div class="tax-info">
        <span class="tax-label">Tax</span>
        <span class="tax-amount">${{ getTotalTaxAmount().toFixed(2) }}</span>
      </div>
    </div>
  </div>

  <!-- 固定在顶部的同步进度条 - 不占用内容空间 -->
  <mat-progress-bar
    *ngIf="isSyncing()"
    mode="indeterminate"
    color="primary"
    class="top-sync-progress">
  </mat-progress-bar>

  <!-- 错误信息显示 -->
  <div *ngIf="hasError()" class="error-message">
    <span>{{ getErrorMessage() }}</span>
    <button (click)="clearError()" class="error-close-btn">×</button>
  </div>

  <!-- 内容区域 -->
  <div class="cart-content">
    <!-- 同步错误状态 - 仅在错误时显示 -->
    <div *ngIf="hasSyncError()" class="sync-error-banner">
      <mat-icon color="warn" class="error-icon">warning</mat-icon>
      <span>Sync failed - retrying automatically</span>
    </div>



    <!-- 空购物车状态 -->
    <div *ngIf="isCartEmpty()" class="empty-cart">
      <mat-icon class="empty-icon">shopping_cart</mat-icon>
      <h3>Your cart is empty</h3>
      <p>Add some delicious items to get started!</p>
    </div>

    <!-- 购物车商品列表 -->
    <div *ngIf="!isCartEmpty()" class="cart-items">
      <div class="items-container">
        <div *ngFor="let item of cartState?.items; let last = last" class="cart-item">
          <div class="item-content">
            <!-- 商品信息区域（可点击编辑） -->
            <div class="item-main-info" (click)="editItem(item)">
              <!-- 商品图片 -->
              <div class="item-image">
                <img [src]="item.image || '/assets/images/placeholder-food.svg'"
                     [alt]="item.name"
                     onerror="this.src='/assets/images/placeholder-food.svg'">
              </div>

              <!-- 商品信息 -->
              <div class="item-info">
                <h4 class="item-name">{{ item.name }}</h4>
                <!-- 选择的规格 -->
                <div *ngIf="item.selectedAttributes && item.selectedAttributes.length > 0"
                     class="item-specs">
                  {{ getSelectedSpecs(item.selectedAttributes) }}
                </div>
                <!-- 备注信息 -->
                <div *ngIf="item.memo && item.memo.trim()" class="item-memo">
                  <mat-icon class="memo-icon">note</mat-icon>
                  <span>{{ item.memo }}</span>
                </div>
                <div class="item-price">${{ item.price.toFixed(2) }}</div>
              </div>
            </div>

            <!-- 数量控制器 -->
            <div class="item-actions" (click)="$event.stopPropagation()">
              <div class="quantity-controls">
                <button (click)="decreaseQuantity(item)"
                        class="quantity-btn">
                  −
                </button>
                <span class="quantity">{{ item.quantity }}</span>
                <button (click)="increaseQuantity(item)"
                        class="quantity-btn">
                  +
                </button>
              </div>
            </div>

            <!-- 同步状态指示器 -->
            <div *ngIf="item.syncStatus === 'pending'" class="sync-indicator">
              <mat-icon class="sync-icon">sync</mat-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 底部结算区域 -->
  <div *ngIf="!isCartEmpty()" class="cart-footer">
    <button mat-raised-button
            color="primary"
            class="checkout-btn"
            [disabled]="isSyncing()"
            (click)="proceedToCheckout()">
      <span *ngIf="!isSyncing()">Checkout</span>
      <span *ngIf="isSyncing()">Syncing...</span>
    </button>
  </div>
</div>
