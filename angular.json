{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": "1e1de97b-a744-405a-8b5a-0397bb3d01ce"}, "newProjectRoot": "projects", "projects": {"demo": {"architect": {"build": {"builder": "@angular-devkit/build-angular:application", "configurations": {"development": {"extractLicenses": false, "namedChunks": true, "optimization": false, "sourceMap": true}, "production": {"aot": true, "extractLicenses": true, "namedChunks": false, "optimization": true, "outputHashing": "all", "sourceMap": false, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}]}, "staging": {"aot": true, "extractLicenses": true, "namedChunks": true, "optimization": true, "outputHashing": "all", "sourceMap": true}}, "options": {"assets": ["src/favicon.ico", "src/assets", {"glob": "**/*", "input": "node_modules/ngx-extended-pdf-viewer/assets", "output": "/assets/"}, "src/staticwebapp.config.json"], "index": "src/index.html", "browser": "src/main.ts", "outputPath": "dist/demo", "polyfills": ["zone.js"], "scripts": [], "styles": ["src/global_styles.css"], "tsConfig": "tsconfig.app.json"}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"development": {"buildTarget": "demo:build:development"}, "production": {"buildTarget": "demo:build:production"}, "staging": {"buildTarget": "demo:build:staging"}}, "defaultConfiguration": "development"}}, "prefix": "app", "projectType": "application", "root": "", "schematics": {}, "sourceRoot": "src"}}, "version": 1}