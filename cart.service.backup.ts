﻿import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { DeliOrderingFoodCartClient, CreateFoodCartRequest, FoodCartResponse, InsertFoodCartItemBody, ProductAttributeItem, AddFoodCartItemRequest, DeleteFoodCartItemResponse, UpdateFoodCartItemRequest, DeliOrderingFoodOrderClient, TransactionOrderResponse } from './backoffice';

// 使用API客户端生成的原生类型

// 简化的属性选择接口 - 用于内部状态管理
export interface AttributeSelection {
  groupId: string;
  groupName: string;
  items: Array<{
    id: string;
    name: string;
    price?: number;
  }>;
}

// 新的API属性接口 - 匹配后端更新的结构
export interface SelectedAttribute {
  id: string;
  name: string;
  price: number;
}

// 简化的购物车商品接口
export interface CartItem {
  id: string;
  foodItemId: string;
  name: string;
  description?: string;
  image?: string;
  price: number;
  quantity: number;
  basePrice: number; // 商品原始基础价格
  selectedAttributes: AttributeSelection[];
}

// 简化的购物车状态接口
export interface CartState {
  id: string | null;
  items: CartItem[];
  totalAmount: number;
  totalTaxAmount: number; // 新增税额字段
  itemCount: number;
  loading: boolean;
  error: string | null;
}

@Injectable({
  providedIn: 'root'
})
export class CartService {
  // 简化的购物车状态
  private cartState: CartState = {
    id: null,
    items: [],
    totalAmount: 0,
    totalTaxAmount: 0, // 初始化税额为0
    itemCount: 0,
    loading: false,
    error: null
  };

  // 状态主题
  private cartStateSubject = new BehaviorSubject<CartState>(this.cartState);
  private cartSidebarOpenSubject = new BehaviorSubject<boolean>(false);

  // 公共观察者
  public cartState$ = this.cartStateSubject.asObservable();
  public cartSidebarOpen$ = this.cartSidebarOpenSubject.asObservable();

  constructor(
    private cartClient: DeliOrderingFoodCartClient,
    private orderClient: DeliOrderingFoodOrderClient
  ) {}

  /**
   * 检查是否已有购物车
   */
  hasCart(): boolean {
    return this.cartState.id !== null && this.cartState.id !== '';
  }

  /**
   * 查找购物车中是否已存在相同商品
   * @param productConfig 商品配置信息
   * @returns 存在的商品项或null
   */
  findExistingItem(productConfig: any): CartItem | null {
    if (!this.hasCart() || this.cartState.items.length === 0) {
      return null;
    }

    return this.cartState.items.find(item =>
      this.isSameProduct(item, productConfig)
    ) || null;
  }

  /**
   * 判断两个商品是否相同（商品ID和规格都相同）
   * @param cartItem 购物车中的商品
   * @param productConfig 要添加的商品配置
   * @returns 是否相同
   */
  private isSameProduct(cartItem: CartItem, productConfig: any): boolean {
    // 1. 检查商品ID是否相同
    if (cartItem.foodItemId !== productConfig.foodItemId) {
      return false;
    }

    // 2. 检查规格选择是否相同
    const cartAttributes = cartItem.selectedAttributes || [];
    const configAttributes = productConfig.selectedAttributes || [];

    // 如果规格数量不同，则不相同
    if (cartAttributes.length !== configAttributes.length) {
      return false;
    }

    // 检查每个规格组是否匹配
    for (const cartAttr of cartAttributes) {
      const matchingConfigAttr = configAttributes.find(
        (configAttr: any) => configAttr.groupId === cartAttr.groupId
      );

      if (!matchingConfigAttr) {
        return false;
      }

      // 检查规格组内的选项是否完全匹配
      if (!this.areAttributeItemsEqual(cartAttr.items, matchingConfigAttr.items)) {
        return false;
      }
    }

    return true;
  }

  /**
   * 比较两个规格选项数组是否相同
   */
  private areAttributeItemsEqual(items1: any[], items2: any[]): boolean {
    if (items1.length !== items2.length) {
      return false;
    }

    // 按ID排序后比较
    const sorted1 = items1.map(item => item.id).sort();
    const sorted2 = items2.map(item => item.id).sort();

    return sorted1.every((id, index) => id === sorted2[index]);
  }

  /**
   * 添加商品到购物车（统一接口）
   * 自动判断是创建购物车还是添加到现有购物车
   * @param storeId 商店ID
   * @param productConfig 商品配置信息
   * @returns Observable<FoodCartResponse>
   */
  addItemToCart(storeId: number, productConfig: any): Observable<FoodCartResponse> {
    console.log('添加商品到购物车:', {
      hasCart: this.hasCart(),
      cartId: this.cartState.id,
      storeId,
      productConfig
    });

    if (this.hasCart()) {
      // 检查是否存在相同商品
      const existingItem = this.findExistingItem(productConfig);

      if (existingItem) {
        console.log('发现相同商品，合并数量:', {
          existingItem: existingItem,
          currentQuantity: existingItem.quantity,
          addQuantity: productConfig.quantity,
          newQuantity: existingItem.quantity + productConfig.quantity
        });

        // 合并数量：现有数量 + 要添加的数量
        const newQuantity = existingItem.quantity + productConfig.quantity;
        return this.updateItemQuantity(existingItem, newQuantity);
      } else {
        console.log('未发现相同商品，添加新商品到现有购物车');
        return this.addToExistingCart(storeId, productConfig);
      }
    } else {
      console.log('购物车不存在，创建新购物车');
      return this.createCartWithItem(storeId, productConfig);
    }
  }

  /**
   * 创建购物车并添加商品
   * @param storeId 商店ID
   * @param productConfig 商品配置信息
   * @returns Observable<FoodCartResponse>
   */
  createCartWithItem(storeId: number, productConfig: any): Observable<FoodCartResponse> {
    console.log('开始创建购物车:', { storeId, productConfig });

    // 设置加载状态
    this.updateLoadingState(true);

    // 构建API请求数据 - 使用API文档格式
    const attributeGroups = this.convertToAttributeGroups(productConfig.selectedAttributes);

    // 创建InsertFoodCartItemBody，使用API文档格式的包装对象
    const foodCartItem = new InsertFoodCartItemBody();
    foodCartItem.foodItem_ID = productConfig.foodItemId;
    foodCartItem.quantity = productConfig.quantity;

    // 传入基础价格，让后端基于此计算最终价格
    const basePrice = productConfig.basePrice || 0;
    foodCartItem.price = basePrice;
    console.log('传入基础价格:', basePrice, '来源:', productConfig.basePrice ? 'productConfig.basePrice' : '默认值0');
    // 设置选择的属性
    foodCartItem.selectedAttibutes = attributeGroups;

    const request = new CreateFoodCartRequest({
      storeId: storeId,
      foodCartItem: foodCartItem
    });

    console.log('API请求数据（包含新格式的selectedAttibutes）:', {
      storeId: storeId,
      foodCartItem: {
        foodItem_ID: productConfig.foodItemId,
        price: basePrice,
        quantity: productConfig.quantity,
        selectedAttibutes: attributeGroups
      }
    });

    return this.cartClient.createCart(request).pipe(
      map(response => {
        console.log('创建购物车API响应:', response);

        if (response.result) {
          // 更新本地购物车状态
          this.updateCartStateFromResponse(response.result, productConfig);
          console.log('购物车状态已更新');
        }

        // 重置加载状态
        this.updateLoadingState(false);

        return response.result!;
      }),
      catchError(error => {
        console.error('创建购物车失败:', error);
        this.updateLoadingState(false);
        throw error;
      })
    );
  }

  /**
   * 将本地属性选择格式转换为API要求的格式
   * 新API期望的格式：ProductAttributeItem[]
   */
  private convertToAttributeGroups(selectedAttributes: AttributeSelection[]): ProductAttributeItem[] {
    if (!selectedAttributes || selectedAttributes.length === 0) {
      return [];
    }

    console.log('转换前的属性数据:', selectedAttributes);

    // 将所有选中的属性项扁平化为ProductAttributeItem数组
    const result = selectedAttributes.flatMap(attr =>
      attr.items.map(item => new ProductAttributeItem({
        id: item.id,
        name: item.name,
        price: item.price || 0
      }))
    );

    console.log('转换后的selectedAttibutes:', result);
    return result;
  }

  /**
   * 根据API响应更新购物车状态
   */
  private updateCartStateFromResponse(response: FoodCartResponse, productConfig?: any): void {
    if (!response.cart || !response.item) {
      console.warn('API响应缺少购物车或商品信息');
      return;
    }

    console.log('=== API响应数据详细分析 ===');
    console.log('完整的response.item:', response.item);
    console.log('response.item.selectedAttibutes:', response.item.selectedAttibutes);
    console.log('response.item.price:', response.item.price);
    console.log('response.item.subtotal:', response.item.subtotal);
    console.log('response.cart.totalAmount:', response.cart.totalAmount);
    console.log('productConfig.totalPrice:', productConfig?.totalPrice);

    // 检查价格字段的类型和值
    console.log('价格字段类型检查:');
    console.log('- response.item.price 类型:', typeof response.item.price, '值:', response.item.price);
    console.log('- response.item.subtotal 类型:', typeof response.item.subtotal, '值:', response.item.subtotal);
    console.log('- response.cart.totalAmount 类型:', typeof response.cart.totalAmount, '值:', response.cart.totalAmount);

    // 创建新的购物车商品，使用API返回的完整属性信息（现在包含名称）
    const newCartItem: CartItem = {
      id: response.item.id!,
      foodItemId: response.item.foodItem_ID!,
      name: productConfig?.name || '未知商品',
      description: productConfig?.description,
      image: productConfig?.image,
      price: response.item.subtotal || response.item.price || 0, // 优先使用subtotal，fallback到price
      quantity: response.item.quantity || 1,
      basePrice: productConfig?.basePrice || 0, // 存储原始基础价格
      selectedAttributes: this.convertFromAttributeGroups(response.item.selectedAttibutes || [])
    };

    console.log('=== 创建的购物车商品 ===');
    console.log('newCartItem.selectedAttributes:', newCartItem.selectedAttributes);

    // 更新购物车状态
    this.cartState = {
      id: response.cart.id!,
      items: [newCartItem],
      totalAmount: response.cart.totalAmount || 0,
      totalTaxAmount: response.cart.totalTaxAmount || 0, // 添加税额字段
      itemCount: 1,
      loading: false,
      error: null
    };

    // 发布状态更新
    this.cartStateSubject.next(this.cartState);
  }

  /**
   * 将API的简化属性格式转换为本地格式
   * 新API返回格式：ProductAttributeItem[]
   */
  private convertFromAttributeGroups(selectedAttibutes: ProductAttributeItem[]): AttributeSelection[] {
    console.log('=== 转换API返回的selectedAttibutes ===');
    console.log('输入的selectedAttibutes:', selectedAttibutes);

    if (!selectedAttibutes || selectedAttibutes.length === 0) {
      return [];
    }

    // 由于新API不再有分组概念，我们创建一个默认分组来保持内部数据结构一致
    const result: AttributeSelection[] = [{
      groupId: 'default-group',
      groupName: 'Selected Options',
      items: selectedAttibutes.map((item: ProductAttributeItem) => ({
        id: item.id || '',
        name: item.name || '',
        price: item.price || 0
      }))
    }];

    console.log('转换后的AttributeSelection:', result);
    return result;
  }

  /**
   * 向现有购物车添加商品
   * @param storeId 商店ID
   * @param productConfig 商品配置信息
   * @returns Observable<FoodCartResponse>
   */
  addToExistingCart(storeId: number, productConfig: any): Observable<FoodCartResponse> {
    if (!this.hasCart()) {
      throw new Error('没有可用的购物车，请先创建购物车');
    }

    console.log('向现有购物车添加商品:', {
      storeId,
      cartId: this.cartState.id,
      productConfig
    });

    // 设置加载状态
    this.updateLoadingState(true);

    // 构建API请求数据 - 使用正确的API格式
    const apiAttributes = this.convertToAttributeGroups(productConfig.selectedAttributes);

    // 创建InsertFoodCartItemBody，使用包装对象
    const foodCartItem = new InsertFoodCartItemBody();
    foodCartItem.foodItem_ID = productConfig.foodItemId;
    foodCartItem.quantity = productConfig.quantity;

    // 传入基础价格，让后端基于此计算最终价格
    const basePrice = productConfig.basePrice || 0;
    foodCartItem.price = basePrice;
    console.log('传入基础价格:', basePrice, '来源:', productConfig.basePrice ? 'productConfig.basePrice' : '默认值0');
    // 设置API格式的属性数据
    foodCartItem.selectedAttibutes = apiAttributes;

    const request = new AddFoodCartItemRequest({
      storeId: storeId,
      cartId: this.cartState.id!,
      foodCartItem: foodCartItem
    });

    console.log('添加商品API请求数据（包含新格式的selectedAttibutes）:', {
      storeId: storeId,
      cartId: this.cartState.id,
      foodCartItem: {
        foodItem_ID: productConfig.foodItemId,
        price: basePrice,
        quantity: productConfig.quantity,
        selectedAttibutes: apiAttributes
      }
    });

    return this.cartClient.addCartItem(request).pipe(
      map(response => {
        console.log('添加商品API响应:', response);

        if (response.result) {
          // 更新购物车状态
          this.updateCartStateFromAddResponse(response.result, productConfig);
          console.log('购物车状态已更新');
        }

        // 重置加载状态
        this.updateLoadingState(false);

        return response.result!;
      }),
      catchError(error => {
        console.error('添加商品到购物车失败:', error);
        this.updateLoadingState(false);
        throw error;
      })
    );
  }

  /**
   * 根据添加商品API响应更新购物车状态
   */
  private updateCartStateFromAddResponse(response: FoodCartResponse, productConfig?: any): void {
    if (!response.cart || !response.item) {
      console.warn('API响应缺少购物车或商品信息');
      return;
    }

    console.log('=== 添加商品API响应数据详细分析 ===');
    console.log('完整的response.item:', response.item);
    console.log('response.item.price:', response.item.price);
    console.log('response.item.subtotal:', response.item.subtotal);
    console.log('response.cart.totalAmount:', response.cart.totalAmount);
    console.log('productConfig.totalPrice:', productConfig?.totalPrice);

    // 检查价格字段的类型和值
    console.log('价格字段类型检查:');
    console.log('- response.item.price 类型:', typeof response.item.price, '值:', response.item.price);
    console.log('- response.item.subtotal 类型:', typeof response.item.subtotal, '值:', response.item.subtotal);
    console.log('- response.cart.totalAmount 类型:', typeof response.cart.totalAmount, '值:', response.cart.totalAmount);

    // 创建新的购物车商品
    const newCartItem: CartItem = {
      id: response.item.id!,
      foodItemId: response.item.foodItem_ID!,
      name: productConfig?.name || '未知商品',
      description: productConfig?.description,
      image: productConfig?.image,
      price: response.item.subtotal || response.item.price || 0, // 优先使用subtotal，fallback到price
      quantity: response.item.quantity || 1,
      basePrice: productConfig?.basePrice || 0, // 存储原始基础价格
      selectedAttributes: this.convertFromAttributeGroups(response.item.selectedAttibutes || [])
    };

    console.log('=== 创建的新购物车商品 ===');
    console.log('newCartItem:', newCartItem);

    // 添加到现有购物车商品列表
    this.cartState.items.push(newCartItem);

    // 更新购物车总信息
    this.cartState.totalAmount = response.cart.totalAmount || 0;
    this.cartState.totalTaxAmount = response.cart.totalTaxAmount || 0; // 添加税额字段
    this.cartState.itemCount = this.cartState.items.length;
    this.cartState.loading = false;
    this.cartState.error = null;

    // 发布状态更新
    this.cartStateSubject.next(this.cartState);
  }

  /**
   * 删除购物车商品
   * @param item 要删除的购物车商品
   * @returns Observable<DeleteFoodCartItemResponse>
   */
  removeItem(item: CartItem): Observable<DeleteFoodCartItemResponse> {
    console.log('删除购物车商品:', {
      cartItemId: item.id,
      itemName: item.name
    });

    // 设置加载状态
    this.updateLoadingState(true);

    const storeId = 2; // 固定使用storeId=2

    return this.cartClient.deleteCartItem(storeId, item.id).pipe(
      map(response => {
        console.log('删除商品API响应:', response);

        if (response.result) {
          // 更新购物车状态
          this.updateCartStateAfterDelete(item, response.result);
          console.log('购物车状态已更新');
        }

        // 重置加载状态
        this.updateLoadingState(false);

        return response.result!;
      }),
      catchError(error => {
        console.error('删除商品失败:', error);
        this.updateLoadingState(false);
        throw error;
      })
    );
  }

  /**
   * 删除商品后更新购物车状态
   */
  private updateCartStateAfterDelete(deletedItem: CartItem, response: DeleteFoodCartItemResponse): void {
    // 从商品列表中移除该商品
    this.cartState.items = this.cartState.items.filter(item => item.id !== deletedItem.id);

    // 更新购物车总金额和税额（从新的API响应结构获取）
    this.cartState.totalAmount = response.cart?.totalAmount || 0;
    this.cartState.totalTaxAmount = response.cart?.totalTaxAmount || 0;

    // 更新商品数量
    this.cartState.itemCount = this.cartState.items.length;

    // 如果购物车为空，重置购物车ID
    if (this.cartState.items.length === 0) {
      console.log('购物车已清空，重置购物车状态');
      this.cartState.id = null;
      this.cartState.totalAmount = 0;
      this.cartState.totalTaxAmount = 0;
      this.cartState.itemCount = 0;
    }

    this.cartState.loading = false;
    this.cartState.error = null;

    // 发布状态更新
    this.cartStateSubject.next(this.cartState);
  }

  /**
   * 更新购物车商品数量
   * @param item 要更新的购物车商品
   * @param newQuantity 新的数量
   * @returns Observable<FoodCartResponse>
   */
  updateItemQuantity(item: CartItem, newQuantity: number): Observable<FoodCartResponse> {
    if (newQuantity < 1) {
      throw new Error('商品数量不能小于1');
    }

    console.log('更新商品数量:', {
      cartItemId: item.id,
      itemName: item.name,
      oldQuantity: item.quantity,
      newQuantity: newQuantity
    });

    // 设置加载状态
    this.updateLoadingState(true);

    const storeId = 2; // 固定使用storeId=2

    // 构建API请求数据 - 使用正确的API格式
    const apiAttributes = this.convertToAttributeGroups(item.selectedAttributes);

    // 使用存储的原始基础价格

    const request = new UpdateFoodCartItemRequest({
      storeId: storeId,
      cartItemId: item.id,
      quantity: newQuantity,
      price: item.basePrice, // 使用存储的原始基础价格
      selectedAttibutes: apiAttributes
    });

    console.log('更新数量API请求数据:', {
      storeId: storeId,
      cartItemId: item.id,
      quantity: newQuantity,
      price: item.basePrice,
      selectedAttibutes: apiAttributes,
      说明: '使用存储的原始基础价格，不进行复杂计算'
    });

    return this.cartClient.updateCartItem(request).pipe(
      map(response => {
        console.log('更新数量API响应:', response);

        if (response.result) {
          // 更新购物车状态
          this.updateCartStateFromUpdateResponse(response.result, item);
          console.log('购物车状态已更新');
        }

        // 重置加载状态
        this.updateLoadingState(false);

        return response.result!;
      }),
      catchError(error => {
        console.error('更新商品数量失败:', error);
        this.updateLoadingState(false);
        throw error;
      })
    );
  }

  /**
   * 增加商品数量
   * @param item 要增加数量的购物车商品
   * @returns Observable<FoodCartResponse>
   */
  increaseQuantity(item: CartItem): Observable<FoodCartResponse> {
    return this.updateItemQuantity(item, item.quantity + 1);
  }

  /**
   * 减少商品数量
   * @param item 要减少数量的购物车商品
   * @returns Observable<FoodCartResponse>
   */
  decreaseQuantity(item: CartItem): Observable<FoodCartResponse> {
    if (item.quantity <= 1) {
      throw new Error('商品数量已经是最小值，无法继续减少');
    }
    return this.updateItemQuantity(item, item.quantity - 1);
  }

  /**
   * 根据更新商品API响应更新购物车状态
   */
  private updateCartStateFromUpdateResponse(response: FoodCartResponse, originalItem: CartItem): void {
    if (!response.cart || !response.item) {
      console.warn('API响应缺少购物车或商品信息');
      return;
    }

    // 找到并更新对应的商品
    const itemIndex = this.cartState.items.findIndex(item => item.id === originalItem.id);
    if (itemIndex !== -1) {
      // 更新商品信息
      this.cartState.items[itemIndex] = {
        ...this.cartState.items[itemIndex],
        quantity: response.item.quantity || 1,
        price: response.item.subtotal || response.item.price || 0 // 优先使用subtotal，fallback到price
      };
    }

    // 更新购物车总信息
    this.cartState.totalAmount = response.cart.totalAmount || 0;
    this.cartState.totalTaxAmount = response.cart.totalTaxAmount || 0; // 添加税额字段
    this.cartState.itemCount = this.cartState.items.reduce((total, item) => total + item.quantity, 0);
    this.cartState.loading = false;
    this.cartState.error = null;

    // 发布状态更新
    this.cartStateSubject.next(this.cartState);
  }

  /**
   * 设置错误信息
   * @param error 错误信息
   */
  setError(error: string): void {
    this.cartState.error = error;
    this.cartState.loading = false;
    this.cartStateSubject.next(this.cartState);
  }

  /**
   * 清除错误信息
   */
  clearError(): void {
    this.cartState.error = null;
    this.cartStateSubject.next(this.cartState);
  }

  /**
   * 更新加载状态
   */
  private updateLoadingState(loading: boolean): void {
    this.cartState.loading = loading;
    // 开始加载时清除之前的错误
    if (loading) {
      this.cartState.error = null;
    }
    this.cartStateSubject.next(this.cartState);
  }





  /**
   * 更新购物车商品
   * @param storeId 商店ID
   * @param cartItemId 购物车商品ID
   * @param productConfig 商品配置
   */
  updateCartItem(storeId: number, cartItemId: string, productConfig: any): Observable<any> {
    console.log('更新购物车商品:', { storeId, cartItemId, productConfig });

    // 设置加载状态
    this.updateLoadingState(true);

    const selectedAttributes = this.convertToAttributeGroups(productConfig.selectedAttributes || []);

    const request = new UpdateFoodCartItemRequest({
      storeId: storeId,
      cartItemId: cartItemId,
      quantity: productConfig.quantity,
      selectedAttibutes: selectedAttributes
    });

    // 传入基础价格，让后端基于此计算最终价格
    const basePrice = productConfig.basePrice || 0;
    request.price = basePrice;
    console.log('更新商品时传入基础价格:', basePrice, '来源:', productConfig.basePrice ? 'productConfig.basePrice' : '默认值0');

    console.log('=== 更新购物车商品API请求 ===');
    console.log('storeId:', storeId);
    console.log('cartItemId:', cartItemId);
    console.log('quantity:', productConfig.quantity);
    console.log('basePrice:', basePrice);
    console.log('selectedAttibutes:', selectedAttributes);
    console.log('期望的价格计算: basePrice × quantity + 选项价格 × quantity');
    console.log('=== API请求数据完成 ===');

    return this.cartClient.updateCartItem(request).pipe(
      map((response: any) => {
        console.log('=== 更新购物车商品API响应 ===');
        console.log('完整响应:', response);
        console.log('response.result.item.price:', response.result?.item?.price);
        console.log('response.result.item.subtotal:', response.result?.item?.subtotal);
        console.log('response.result.item.quantity:', response.result?.item?.quantity);
        console.log('response.result.cart.totalAmount:', response.result?.cart?.totalAmount);
        console.log('=== API响应分析完成 ===');
        this.updateCartStateFromResponse(response.result!, productConfig);
        return response.result;
      }),
      catchError(error => {
        console.error('更新购物车商品失败:', error);
        this.setError('更新商品失败');
        this.updateLoadingState(false);
        throw error;
      })
    );
  }

  /**
   * 获取购物车总税额
   */
  getTotalTaxAmount(): number {
    return this.cartState.totalTaxAmount;
  }

  /**
   * 结算购物车
   * @param storeId 商店ID
   * @returns Observable<TransactionOrderResponse>
   */
  checkout(storeId: number): Observable<TransactionOrderResponse> {
    if (!this.cartState.id) {
      throw new Error('购物车ID不存在，无法进行结算');
    }

    if (this.cartState.items.length === 0) {
      throw new Error('购物车为空，无法进行结算');
    }

    console.log('=== 开始结算 ===');
    console.log('storeId:', storeId);
    console.log('cartId:', this.cartState.id);
    console.log('购物车商品数量:', this.cartState.items.length);
    console.log('购物车总金额:', this.cartState.totalAmount);
    console.log('购物车状态:', this.cartState);
    console.log('API URL将会是:', `/api/Deliordering/food/order/${storeId}/${this.cartState.id}`);

    this.cartState.loading = true;
    this.cartStateSubject.next(this.cartState);

    return this.orderClient.createOrder(storeId, this.cartState.id).pipe(
      map(response => {
        console.log('=== 结算成功 ===');
        console.log('完整响应:', response);
        console.log('结算结果:', response.result);

        // 结算成功后清空购物车
        this.clearCart();

        return response.result!;
      }),
      catchError(error => {
        console.error('=== 结算失败 ===');
        console.error('错误详情:', error);
        console.error('错误状态码:', error.status);
        console.error('错误消息:', error.message);
        console.error('错误响应体:', error.response);

        // 重置loading状态
        this.cartState.loading = false;

        // 根据错误类型提供不同的错误信息
        let errorMessage = 'Checkout failed, please try again';
        if (error.status === 500) {
          errorMessage = 'Server internal error, please contact customer service or try again later';
        } else if (error.status === 404) {
          errorMessage = 'Checkout service is temporarily unavailable, please try again later';
        } else if (error.status === 400) {
          errorMessage = 'Cart data is invalid, please refresh the page and try again';
        }

        this.setError(errorMessage);
        throw error;
      })
    );
  }

  /**
   * 清空购物车状态
   */
  private clearCart(): void {
    this.cartState = {
      id: null,
      items: [],
      totalAmount: 0,
      totalTaxAmount: 0,
      itemCount: 0,
      loading: false,
      error: null
    };
    this.cartStateSubject.next(this.cartState);
    console.log('购物车已清空');
  }

}
