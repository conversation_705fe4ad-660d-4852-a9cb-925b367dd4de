{"runtime": "<PERSON><PERSON><PERSON>", "defaultVariables": null, "documentGenerator": {"fromDocument": {"json": "{\r\n  \"x-generator\": \"NSwag v13.16.1.0 (NJsonSchema v10.7.2.0 (Newtonsoft.Json v13.0.0.0))\",\r\n  \"openapi\": \"3.0.0\",\r\n  \"info\": {\r\n    \"title\": \"Mobile Reporting Backend\",\r\n    \"version\": \"1.0.0\"\r\n  },\r\n  \"servers\": [\r\n    {\r\n      \"url\": \"https://jr8qjxj4-44373.asse.devtunnels.ms\"\r\n    }\r\n  ],\r\n  \"paths\": {\r\n    \"/StoreConnection/DeliProducts\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"StoreConnection\"\r\n        ],\r\n        \"operationId\": \"StoreConnection_GetDeliProducts\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"StoreAccessKey\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/StoreConnection/ProductsCorrelationCoefficient\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"StoreConnection\"\r\n        ],\r\n        \"operationId\": \"StoreConnection_GetProductsCorrelationCoefficient\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"StoreAccessKey\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"PluUpcs\",\r\n            \"in\": \"query\",\r\n            \"style\": \"form\",\r\n            \"explode\": true,\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"nullable\": true,\r\n              \"items\": {\r\n                \"type\": \"string\"\r\n              }\r\n            },\r\n            \"x-position\": 2\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/StoreConnection/Products\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"StoreConnection\"\r\n        ],\r\n        \"operationId\": \"StoreConnection_GetProducts\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"StoreAccessKey\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"Departments\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 2\r\n          },\r\n          {\r\n            \"name\": \"ExcludedDepartments\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"boolean\"\r\n            },\r\n            \"x-position\": 3\r\n          },\r\n          {\r\n            \"name\": \"PluUpcs\",\r\n            \"in\": \"query\",\r\n            \"style\": \"form\",\r\n            \"explode\": true,\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"nullable\": true,\r\n              \"items\": {\r\n                \"type\": \"string\"\r\n              }\r\n            },\r\n            \"x-position\": 4\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/StoreConnection/Promotions\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"StoreConnection\"\r\n        ],\r\n        \"operationId\": \"StoreConnection_GetPromotions\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"StoreAccessKey\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"PluUpcs\",\r\n            \"in\": \"query\",\r\n            \"style\": \"form\",\r\n            \"explode\": true,\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"nullable\": true,\r\n              \"items\": {\r\n                \"type\": \"string\"\r\n              }\r\n            },\r\n            \"x-position\": 2\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/StoreConnection/DiscountCoupons\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"StoreConnection\"\r\n        ],\r\n        \"operationId\": \"StoreConnection_GetDiscounts\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"StoreAccessKeys\",\r\n            \"in\": \"query\",\r\n            \"style\": \"form\",\r\n            \"explode\": true,\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"nullable\": true,\r\n              \"items\": {\r\n                \"type\": \"string\"\r\n              }\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"Startdate\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"format\": \"date-time\"\r\n            },\r\n            \"x-position\": 2\r\n          },\r\n          {\r\n            \"name\": \"Enddate\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"format\": \"date-time\"\r\n            },\r\n            \"x-position\": 3\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"$ref\": \"#/components/schemas/Discount\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/StoreConnection\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"StoreConnection\"\r\n        ],\r\n        \"operationId\": \"StoreConnection_Get\",\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"$ref\": \"#/components/schemas/StoreConnection\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/StoreConnection/Transactions\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"StoreConnection\"\r\n        ],\r\n        \"operationId\": \"StoreConnection_PostTransactions\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeAccessKey\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"orderId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"maxLength\": 50,\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 2\r\n          },\r\n          {\r\n            \"name\": \"source\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"maxLength\": 50,\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 3\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/StoreConnection/OrderRecive\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"StoreConnection\"\r\n        ],\r\n        \"operationId\": \"StoreConnection_PostOrderRecive\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeAccessKey\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"orderId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"maxLength\": 50,\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 2\r\n          },\r\n          {\r\n            \"name\": \"source\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"maxLength\": 50,\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 3\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/BaseData/department\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"BaseData\"\r\n        ],\r\n        \"operationId\": \"BaseData_GetDeaprtmrnts\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"stores\",\r\n            \"in\": \"query\",\r\n            \"style\": \"form\",\r\n            \"explode\": true,\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"nullable\": true,\r\n              \"items\": {\r\n                \"type\": \"integer\",\r\n                \"format\": \"int32\"\r\n              }\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"$ref\": \"#/components/schemas/Department\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/BaseData/vendors\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"BaseData\"\r\n        ],\r\n        \"operationId\": \"BaseData_GetVendors\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"stores\",\r\n            \"in\": \"query\",\r\n            \"style\": \"form\",\r\n            \"explode\": true,\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"nullable\": true,\r\n              \"items\": {\r\n                \"type\": \"integer\",\r\n                \"format\": \"int32\"\r\n              }\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"$ref\": \"#/components/schemas/Vendor\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/BaseData/brands\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"BaseData\"\r\n        ],\r\n        \"operationId\": \"BaseData_GetBrands\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"stores\",\r\n            \"in\": \"query\",\r\n            \"style\": \"form\",\r\n            \"explode\": true,\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"nullable\": true,\r\n              \"items\": {\r\n                \"type\": \"integer\",\r\n                \"format\": \"int32\"\r\n              }\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"$ref\": \"#/components/schemas/Brand\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/BaseData/taxs\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"BaseData\"\r\n        ],\r\n        \"operationId\": \"BaseData_GetTaxs\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"stores\",\r\n            \"in\": \"query\",\r\n            \"style\": \"form\",\r\n            \"explode\": true,\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"nullable\": true,\r\n              \"items\": {\r\n                \"type\": \"integer\",\r\n                \"format\": \"int32\"\r\n              }\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"$ref\": \"#/components/schemas/TaxDto\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/BaseData/department/group\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"BaseData\"\r\n        ],\r\n        \"operationId\": \"BaseData_GetDepartmentGroup\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"stores\",\r\n            \"in\": \"query\",\r\n            \"style\": \"form\",\r\n            \"explode\": true,\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"nullable\": true,\r\n              \"items\": {\r\n                \"type\": \"integer\",\r\n                \"format\": \"int32\"\r\n              }\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"$ref\": \"#/components/schemas/DepartmentGroupDto\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/Bundles/{storeId}/{endTime}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Bundles\"\r\n        ],\r\n        \"summary\": \"查询店铺下时间段的Bundles\",\r\n        \"operationId\": \"Bundles_GetBundles\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"description\": \"店铺ID\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"endTime\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"description\": \"结束时间\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"format\": \"date-time\"\r\n            },\r\n            \"x-position\": 2\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"$ref\": \"#/components/schemas/BundlesDto\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/Bundles/CopyBundle/{sourceStoreId}/{destinationStoreId}/{sourceBundleId}\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Bundles\"\r\n        ],\r\n        \"operationId\": \"Bundles_CopyBundle\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"sourceStoreId\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"destinationStoreId\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 2\r\n          },\r\n          {\r\n            \"name\": \"sourceBundleId\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"format\": \"guid\"\r\n            },\r\n            \"x-position\": 3\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/Categroy/update/catrgory/scale\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Categroy\"\r\n        ],\r\n        \"summary\": \"修改category的scale\",\r\n        \"operationId\": \"Categroy_UpdateCategoryScale\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"categoryId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 2\r\n          },\r\n          {\r\n            \"name\": \"isScale\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"boolean\"\r\n            },\r\n            \"x-position\": 3\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/Companies\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Companies\"\r\n        ],\r\n        \"summary\": \"创建新的公司\",\r\n        \"operationId\": \"Companies_CreatedCompany\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"companyName\",\r\n            \"in\": \"query\",\r\n            \"description\": \"Company Name\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Created Success!\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/CompanyDTO\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"409\": {\r\n            \"description\": \"当前创建的公司名已经存在或者创建失败\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        },\r\n        \"security\": [\r\n          {\r\n            \"Bearer\": [\r\n              \"administrator\"\r\n            ]\r\n          }\r\n        ]\r\n      },\r\n      \"delete\": {\r\n        \"tags\": [\r\n          \"Companies\"\r\n        ],\r\n        \"summary\": \"删除公司\",\r\n        \"operationId\": \"Companies_DeleteCompany\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"id\",\r\n            \"in\": \"query\",\r\n            \"description\": \"Company Id\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"204\": {\r\n            \"description\": \"Delete Success!\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"Not Found!\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"409\": {\r\n            \"description\": \"Delete Faliure!\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        },\r\n        \"security\": [\r\n          {\r\n            \"Bearer\": [\r\n              \"administrator\"\r\n            ]\r\n          }\r\n        ]\r\n      },\r\n      \"put\": {\r\n        \"tags\": [\r\n          \"Companies\"\r\n        ],\r\n        \"summary\": \"更新公司名字\",\r\n        \"operationId\": \"Companies_UpdateCompay\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"id\",\r\n            \"in\": \"query\",\r\n            \"description\": \"Conpany Id\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"companyName\",\r\n            \"in\": \"query\",\r\n            \"description\": \"new Compant Name\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 2\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"204\": {\r\n            \"description\": \"Update Success!\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"Not Found!\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"409\": {\r\n            \"description\": \"新的公司名和旧的公司名重复或者数据库错误\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        },\r\n        \"security\": [\r\n          {\r\n            \"Bearer\": [\r\n              \"administrator\"\r\n            ]\r\n          }\r\n        ]\r\n      },\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Companies\"\r\n        ],\r\n        \"summary\": \"获取所有已存在的公司\",\r\n        \"operationId\": \"Companies_GetCompanies\",\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Companies\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/CompaniesDTO\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"Not Found!\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/stores/Customer/accountnumbers\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Customer\"\r\n        ],\r\n        \"operationId\": \"Customer_GetAccountNumbers\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"companyId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"searchText\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 2\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"type\": \"string\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        },\r\n        \"security\": [\r\n          {\r\n            \"Bearer\": []\r\n          }\r\n        ]\r\n      }\r\n    },\r\n    \"/api/stores/Customer/accountnumbers/details\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Customer\"\r\n        ],\r\n        \"operationId\": \"Customer_GetAccountNumberDetails\",\r\n        \"requestBody\": {\r\n          \"x-name\": \"body\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/AccountNumberDetailsBody\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 1\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"$ref\": \"#/components/schemas/AccountNumberDetailDto\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        },\r\n        \"security\": [\r\n          {\r\n            \"Bearer\": []\r\n          }\r\n        ]\r\n      }\r\n    },\r\n    \"/api/Deliordering/food/category\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"DeliOrderingFoodCategory\"\r\n        ],\r\n        \"summary\": \"获取所有分类\",\r\n        \"operationId\": \"DeliOrderingFoodCategory_GetCategorys\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"$ref\": \"#/components/schemas/FoodCategoryDto\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"DeliOrderingFoodCategory\"\r\n        ],\r\n        \"summary\": \"新增分类\",\r\n        \"operationId\": \"DeliOrderingFoodCategory_AddCategory\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"requestBody\": {\r\n          \"x-name\": \"body\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/InsertFoodCategoryBody\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 2\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/FoodCategoryDto\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"put\": {\r\n        \"tags\": [\r\n          \"DeliOrderingFoodCategory\"\r\n        ],\r\n        \"summary\": \"更新分类\",\r\n        \"operationId\": \"DeliOrderingFoodCategory_UpdateCategory\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"requestBody\": {\r\n          \"x-name\": \"body\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/UpdateFoodCategoryBody\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 2\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"delete\": {\r\n        \"tags\": [\r\n          \"DeliOrderingFoodCategory\"\r\n        ],\r\n        \"summary\": \"删除分类\",\r\n        \"operationId\": \"DeliOrderingFoodCategory_DeleteCategory\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"categoryId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"format\": \"guid\"\r\n            },\r\n            \"x-position\": 2\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/Deliordering/food/category/sortorders\": {\r\n      \"put\": {\r\n        \"tags\": [\r\n          \"DeliOrderingFoodCategory\"\r\n        ],\r\n        \"summary\": \"更新分类顺寻\",\r\n        \"operationId\": \"DeliOrderingFoodCategory_UpdateSortOrders\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"requestBody\": {\r\n          \"x-name\": \"body\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/UpdateSortOrdersBody\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 2\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/Deliordering/food/items\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"DeliOrderingFoodItem\"\r\n        ],\r\n        \"summary\": \"获取所有分类下的商品\",\r\n        \"operationId\": \"DeliOrderingFoodItem_GetFoodItems\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"foodCategoryId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 2\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"$ref\": \"#/components/schemas/FoodItemDto\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"DeliOrderingFoodItem\"\r\n        ],\r\n        \"summary\": \"新增商品\",\r\n        \"operationId\": \"DeliOrderingFoodItem_AddFoodItem\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"requestBody\": {\r\n          \"x-name\": \"body\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/InserFoodItemBody\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 2\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/FoodItemDto\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"delete\": {\r\n        \"tags\": [\r\n          \"DeliOrderingFoodItem\"\r\n        ],\r\n        \"summary\": \"删除商品\",\r\n        \"operationId\": \"DeliOrderingFoodItem_DeleteFoodItem\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"itemId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"format\": \"guid\"\r\n            },\r\n            \"x-position\": 2\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"put\": {\r\n        \"tags\": [\r\n          \"DeliOrderingFoodItem\"\r\n        ],\r\n        \"summary\": \"更新商品\",\r\n        \"operationId\": \"DeliOrderingFoodItem_UpdateFoodItem\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"requestBody\": {\r\n          \"x-name\": \"body\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/UpdateFoodItemBody\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 2\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/Deliordering/food/items/item/details\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"DeliOrderingFoodItem\"\r\n        ],\r\n        \"operationId\": \"DeliOrderingFoodItem_GetFoodItemDetails\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"itemId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"format\": \"guid\"\r\n            },\r\n            \"x-position\": 2\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/FoodItemDetailsDto\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/Deliordering/food/items/sortorders\": {\r\n      \"put\": {\r\n        \"tags\": [\r\n          \"DeliOrderingFoodItem\"\r\n        ],\r\n        \"summary\": \"更新商品排序\",\r\n        \"operationId\": \"DeliOrderingFoodItem_UpdateSortOrders\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"requestBody\": {\r\n          \"x-name\": \"body\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/UpdateSortOrdersBody\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 2\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/Deliordering/food/items/basic\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"DeliOrderingFoodItem\"\r\n        ],\r\n        \"summary\": \"获取商品基本信息\",\r\n        \"operationId\": \"DeliOrderingFoodItem_GetItemBasicInfo\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"pluId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"format\": \"guid\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 2\r\n          },\r\n          {\r\n            \"name\": \"barcode\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 3\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/ItemBasicInfoDto\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/Deliordering/food/image-gallery\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"DeliOrderingFoodItemImageGallery\"\r\n        ],\r\n        \"summary\": \"获取商品下的所有副图\",\r\n        \"operationId\": \"DeliOrderingFoodItemImageGallery_GetImages\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"parentId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"format\": \"guid\"\r\n            },\r\n            \"x-position\": 2\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"$ref\": \"#/components/schemas/FoodImageChildDto\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"DeliOrderingFoodItemImageGallery\"\r\n        ],\r\n        \"summary\": \"添加副图\",\r\n        \"operationId\": \"DeliOrderingFoodItemImageGallery_AddImage\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"requestBody\": {\r\n          \"x-name\": \"items\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"type\": \"array\",\r\n                \"items\": {\r\n                  \"$ref\": \"#/components/schemas/ImageItem\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 2\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"delete\": {\r\n        \"tags\": [\r\n          \"DeliOrderingFoodItemImageGallery\"\r\n        ],\r\n        \"summary\": \"删除副图\",\r\n        \"operationId\": \"DeliOrderingFoodItemImageGallery_DeleteImages\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"requestBody\": {\r\n          \"x-name\": \"items\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"type\": \"array\",\r\n                \"items\": {\r\n                  \"$ref\": \"#/components/schemas/ImageItem\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 2\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/Deliordering/food/image-gallery/sortorders\": {\r\n      \"put\": {\r\n        \"tags\": [\r\n          \"DeliOrderingFoodItemImageGallery\"\r\n        ],\r\n        \"summary\": \"更新副图的循序\",\r\n        \"operationId\": \"DeliOrderingFoodItemImageGallery_UpdateSortOrders\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"requestBody\": {\r\n          \"x-name\": \"body\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/UpdateSortOrdersBody\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 2\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/Devices/{id}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Devices\"\r\n        ],\r\n        \"operationId\": \"Devices_Get\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"id\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"put\": {\r\n        \"tags\": [\r\n          \"Devices\"\r\n        ],\r\n        \"operationId\": \"Devices_Put\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"id\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"requestBody\": {\r\n          \"x-name\": \"body\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/Device\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 2\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/Devices/{id}/records\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Devices\"\r\n        ],\r\n        \"operationId\": \"Devices_PostRecord\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"id\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"requestBody\": {\r\n          \"x-name\": \"body\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/DeviceRecord\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 2\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/Invoices/Upload\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Invoices\"\r\n        ],\r\n        \"operationId\": \"Invoices_UploadDataWithFile\",\r\n        \"requestBody\": {\r\n          \"content\": {\r\n            \"multipart/form-data\": {\r\n              \"schema\": {\r\n                \"type\": \"object\",\r\n                \"properties\": {\r\n                  \"StoreId\": {\r\n                    \"type\": \"integer\",\r\n                    \"format\": \"int32\"\r\n                  },\r\n                  \"SupplierType\": {\r\n                    \"$ref\": \"#/components/schemas/SupplierType\"\r\n                  },\r\n                  \"InvoiceDocuments\": {\r\n                    \"type\": \"string\",\r\n                    \"format\": \"binary\",\r\n                    \"nullable\": true\r\n                  },\r\n                  \"ProductDocuments\": {\r\n                    \"type\": \"array\",\r\n                    \"nullable\": true,\r\n                    \"items\": {\r\n                      \"type\": \"string\",\r\n                      \"format\": \"binary\"\r\n                    }\r\n                  },\r\n                  \"InvoiceAndProductData\": {\r\n                    \"type\": \"string\",\r\n                    \"format\": \"binary\",\r\n                    \"nullable\": true\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/Log\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Log\"\r\n        ],\r\n        \"operationId\": \"Log_DownLoadLog\",\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"delete\": {\r\n        \"tags\": [\r\n          \"Log\"\r\n        ],\r\n        \"operationId\": \"Log_ClearLog\",\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/Login\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Login\"\r\n        ],\r\n        \"summary\": \"使用XYTECH模块登录，获取身份验证的Token\",\r\n        \"operationId\": \"Login_Login\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"user\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"password\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 2\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/transfer\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Mixcode\"\r\n        ],\r\n        \"summary\": \"转移当前店铺的Mixcode数据到目标店铺\",\r\n        \"operationId\": \"Mixcode_TransferMixcode\",\r\n        \"requestBody\": {\r\n          \"x-name\": \"body\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/TransferMixcodeBodyDto\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 1\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/Mixcode\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Mixcode\"\r\n        ],\r\n        \"summary\": \"获取当前店铺下的所有Mixcode\",\r\n        \"operationId\": \"Mixcode_Mixcodes\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"currentStore\",\r\n            \"in\": \"query\",\r\n            \"description\": \"当前店铺\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"type\": \"string\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/nationsbenefits/accountValidation\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Nationsbenefits\"\r\n        ],\r\n        \"operationId\": \"Nationsbenefits_AccountValidation\",\r\n        \"requestBody\": {\r\n          \"x-name\": \"body\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/AVSRequest\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 1\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/AVSResponse\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/nationsbenefits/balanceInquiry\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Nationsbenefits\"\r\n        ],\r\n        \"operationId\": \"Nationsbenefits_BalanceInquiry\",\r\n        \"requestBody\": {\r\n          \"x-name\": \"body\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/BIRequest\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 1\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/BIResponse\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/nationsbenefits/analyze\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Nationsbenefits\"\r\n        ],\r\n        \"operationId\": \"Nationsbenefits_Analyze\",\r\n        \"requestBody\": {\r\n          \"x-name\": \"body\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/AnalyzeRequest\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 1\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/AnalyzeResponse\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/nationsbenefits/redeem\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Nationsbenefits\"\r\n        ],\r\n        \"operationId\": \"Nationsbenefits_Redeem\",\r\n        \"requestBody\": {\r\n          \"x-name\": \"body\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/RedeemRequest\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 1\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/RedeemResponse\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/nationsbenefits/void\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Nationsbenefits\"\r\n        ],\r\n        \"operationId\": \"Nationsbenefits_Void\",\r\n        \"requestBody\": {\r\n          \"x-name\": \"body\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/VoidRequest\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 1\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/VoidResponse\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/nationsbenefits/reversal\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Nationsbenefits\"\r\n        ],\r\n        \"operationId\": \"Nationsbenefits_Reversal\",\r\n        \"requestBody\": {\r\n          \"x-name\": \"body\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/ReversalRequest\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 1\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/ReversalResponse\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/NegativePriceProductMovementReport\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"NegativePriceProductMovementReport\"\r\n        ],\r\n        \"operationId\": \"NegativePriceProductMovementReport_GetNegativePriceProductMovement\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"stores\",\r\n            \"in\": \"query\",\r\n            \"style\": \"form\",\r\n            \"explode\": true,\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"nullable\": true,\r\n              \"items\": {\r\n                \"type\": \"integer\",\r\n                \"format\": \"int32\"\r\n              }\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"begin\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 2\r\n          },\r\n          {\r\n            \"name\": \"end\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 3\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"$ref\": \"#/components/schemas/NegativePriceProductMovementDto\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/approval/transaction/{storeId}/{paymentId}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"PayMent\"\r\n        ],\r\n        \"summary\": \"获取支付信息\",\r\n        \"operationId\": \"PayMent_GetPayMentContent\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"description\": \"店铺id\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"paymentId\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"description\": \"支付Id\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 2\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"404\": {\r\n            \"description\": \"Not Found!\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"200\": {\r\n            \"description\": \"Success!\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/TransactionResponse\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/approval/modify/userPayMent\": {\r\n      \"put\": {\r\n        \"tags\": [\r\n          \"PayMent\"\r\n        ],\r\n        \"summary\": \"验证信用卡用户支付\",\r\n        \"operationId\": \"PayMent_PutUserPayMent\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"description\": \"店铺id\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"paymentId\",\r\n            \"in\": \"query\",\r\n            \"description\": \"支付id\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 2\r\n          }\r\n        ],\r\n        \"requestBody\": {\r\n          \"x-name\": \"userPayment\",\r\n          \"description\": \"用户支付信息\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/UserPayment\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 3\r\n        },\r\n        \"responses\": {\r\n          \"404\": {\r\n            \"description\": \"Not Found!\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/ValidationProblemDetails\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"200\": {\r\n            \"description\": \"Success!\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"504\": {\r\n            \"description\": \"Time Out!\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/ValidationProblemDetails\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"400\": {\r\n            \"description\": \"Payment Failure!\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/ValidationProblemDetails\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/approval/modify/status\": {\r\n      \"put\": {\r\n        \"tags\": [\r\n          \"PayMent\"\r\n        ],\r\n        \"summary\": \"修改状态\",\r\n        \"operationId\": \"PayMent_PutPayMentStatus\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"description\": \"店铺id\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"paymentId\",\r\n            \"in\": \"query\",\r\n            \"description\": \"支付id\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 2\r\n          },\r\n          {\r\n            \"name\": \"statusText\",\r\n            \"in\": \"query\",\r\n            \"description\": \"状态文本\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 3\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"404\": {\r\n            \"description\": \"Not Found!\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"200\": {\r\n            \"description\": \"Success!\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/posuserlogintoken/{storeId}/{registerNumber}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"PosuserLogIntToken\"\r\n        ],\r\n        \"summary\": \"获取Token 重定向\",\r\n        \"operationId\": \"PosuserLogIntToken_GetLoginToken\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"description\": \"商店id\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"registerNumber\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"description\": \"收银台id\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 2\r\n          },\r\n          {\r\n            \"name\": \"devmode\",\r\n            \"in\": \"query\",\r\n            \"description\": \"true:开发者模式 false: 生产模式\",\r\n            \"schema\": {\r\n              \"type\": \"boolean\",\r\n              \"default\": false\r\n            },\r\n            \"x-position\": 3\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"302\": {\r\n            \"description\": \"重定向\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"Not Found!\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/userLogin/{userLoginToken}/{lanenumber}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"PosuserLogIntToken\"\r\n        ],\r\n        \"summary\": \"登录\",\r\n        \"operationId\": \"PosuserLogIntToken_UserLogin\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"userLoginToken\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"description\": \"Token\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"lanenumber\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"description\": \"手机号\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 2\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"404\": {\r\n            \"description\": \"Not Found!\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"406\": {\r\n            \"description\": \"参数错误\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/ValidationProblemDetails\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"401\": {\r\n            \"description\": \"Token 过期\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/ValidationProblemDetails\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"200\": {\r\n            \"description\": \"请求成功\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/CustomerResponse\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/userRegisterTask/{registerToken}\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"PosuserLogIntToken\"\r\n        ],\r\n        \"summary\": \"注册\",\r\n        \"operationId\": \"PosuserLogIntToken_UserRegister\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"registerToken\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"description\": \"注册Token\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"requestBody\": {\r\n          \"x-name\": \"customerItem\",\r\n          \"description\": \"新用户数据\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/RegisterRequest\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 2\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"请求成功\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/CustomerResponse\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"406\": {\r\n            \"description\": \"参数错误\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/ValidationProblemDetails\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"Not Fond\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"401\": {\r\n            \"description\": \"Token 过期\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/ValidationProblemDetails\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/{storeId}/{phoneNumber}\": {\r\n      \"put\": {\r\n        \"tags\": [\r\n          \"PosuserLogIntToken\"\r\n        ],\r\n        \"summary\": \"修改用户数据\",\r\n        \"operationId\": \"PosuserLogIntToken_UserUpdate\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"description\": \"店铺id\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"phoneNumber\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"description\": \"用户账户\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 2\r\n          }\r\n        ],\r\n        \"requestBody\": {\r\n          \"x-name\": \"custom\",\r\n          \"description\": \"用户数据\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/CustomUpdateRequest\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 3\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"更新成功\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"Not Found\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"406\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/ValidationProblemDetails\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/customer\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"PosuserLogIntToken\"\r\n        ],\r\n        \"operationId\": \"PosuserLogIntToken_GetCustomer\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"phoneNumber\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 2\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/test\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"PosuserLogIntToken\"\r\n        ],\r\n        \"operationId\": \"PosuserLogIntToken_GetPointBalance\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"PointBalanceAccessKey\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"accountNumber\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 2\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"integer\",\r\n                  \"format\": \"int32\",\r\n                  \"nullable\": true\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/Product/details\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Product\"\r\n        ],\r\n        \"summary\": \"根据条形码获取当前公司下所有店铺符合条件的商品详细数据\",\r\n        \"operationId\": \"Product_GetProductDetails\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"companyId\",\r\n            \"in\": \"query\",\r\n            \"description\": \"公司id\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"barcode\",\r\n            \"in\": \"query\",\r\n            \"description\": \"商品条形码\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 2\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/CompanyProductsDTO\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/Product/basic-info\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Product\"\r\n        ],\r\n        \"summary\": \"根据条形码获取当前公司下所有店铺符合条件的商品基本数据\",\r\n        \"operationId\": \"Product_GetBasicProductInfo\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"companyId\",\r\n            \"in\": \"query\",\r\n            \"description\": \"公司ID\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"barcode\",\r\n            \"in\": \"query\",\r\n            \"description\": \"条形码\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 2\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/Product/Image\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Product\"\r\n        ],\r\n        \"summary\": \"获取商品图片\",\r\n        \"operationId\": \"Product_GetProductImage\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"imageId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 2\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/ProblemDetails\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/ProductImages/{pluUpc}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"ProductImages\"\r\n        ],\r\n        \"summary\": \"获取数据库商品图片\",\r\n        \"operationId\": \"ProductImages_Get\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"pluUpc\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"storeAccessKey\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 2\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/ProblemDetails\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"delete\": {\r\n        \"tags\": [\r\n          \"ProductImages\"\r\n        ],\r\n        \"summary\": \"删除图片（目前仅用于测试）\",\r\n        \"operationId\": \"ProductImages_Delete\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"pluUpc\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"storeAccessKey\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 2\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"204\": {\r\n            \"description\": \"\"\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/ProblemDetails\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/ProductImages/Synchronization\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"ProductImages\"\r\n        ],\r\n        \"summary\": \"同步商品图片\",\r\n        \"operationId\": \"ProductImages_Synchronization\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeAccessKey\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"requestBody\": {\r\n          \"x-name\": \"body\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/ProductImageSynchronizationBody\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 2\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"type\": \"string\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/ProblemDetails\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/ProductInfoCompletion/plc\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"ProductInfoCompletion\"\r\n        ],\r\n        \"operationId\": \"ProductInfoCompletion_PostProductInfoCompletion\",\r\n        \"requestBody\": {\r\n          \"x-name\": \"Request\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/BarcodesBody\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 1\r\n        },\r\n        \"responses\": {\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"$ref\": \"#/components/schemas/BarcodeInfo\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/ProductInfoCompletion/description/{PLUUPC}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"ProductInfoCompletion\"\r\n        ],\r\n        \"operationId\": \"ProductInfoCompletion_GetProductDescription\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"PLUUPC\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/ServiceResponseOfProductResult\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"400\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/ServiceResponseOfProductResult\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/ProductManagement/product/code\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"ProductManagement\"\r\n        ],\r\n        \"summary\": \"根据条件获取指定店铺下的商品条码(在商品条码和描述中只能二选其一)\",\r\n        \"operationId\": \"ProductManagement_StoreProductCode\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"stores\",\r\n            \"in\": \"query\",\r\n            \"style\": \"form\",\r\n            \"explode\": true,\r\n            \"description\": \"店铺ID\",\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"nullable\": true,\r\n              \"items\": {\r\n                \"type\": \"integer\",\r\n                \"format\": \"int32\"\r\n              }\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"barcode\",\r\n            \"in\": \"query\",\r\n            \"description\": \"商品条码\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 2\r\n          },\r\n          {\r\n            \"name\": \"description\",\r\n            \"in\": \"query\",\r\n            \"description\": \"商品描述\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 3\r\n          },\r\n          {\r\n            \"name\": \"department\",\r\n            \"in\": \"query\",\r\n            \"description\": \"部门描述\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 4\r\n          },\r\n          {\r\n            \"name\": \"vendor\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 5\r\n          },\r\n          {\r\n            \"name\": \"brand\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 6\r\n          },\r\n          {\r\n            \"name\": \"deptGroup\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 7\r\n          },\r\n          {\r\n            \"name\": \"excludeUniformValues\",\r\n            \"in\": \"query\",\r\n            \"style\": \"form\",\r\n            \"explode\": true,\r\n            \"description\": \"根据（Description,Department,Vendor,Brand,Tax,Price)排除多家店铺相同的数据,例子：new string[]{\\\"Description\\\",\\\"Department\\\"}\",\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"nullable\": true,\r\n              \"items\": {\r\n                \"type\": \"string\"\r\n              }\r\n            },\r\n            \"x-position\": 8\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"店铺条码数组\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"$ref\": \"#/components/schemas/KeyValuePairedOfIntegerAndString\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"put\": {\r\n        \"tags\": [\r\n          \"ProductManagement\"\r\n        ],\r\n        \"summary\": \"更新商品信息\",\r\n        \"description\": \"不需要更新的字段设置值为：null\\n更新Tax时，选择Department为税率源，则TaxIds值为null\",\r\n        \"operationId\": \"ProductManagement_UpdateProduct\",\r\n        \"requestBody\": {\r\n          \"x-name\": \"updateProduct\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/UpdateProductDto\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 1\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/ProductManagement/product/code/info\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"ProductManagement\"\r\n        ],\r\n        \"summary\": \"根据条形码获取指定店铺下的商品信息\",\r\n        \"description\": \"税率类型：\\\"Follow Department Tax Policy\\\" 部门型 | \\\"Follow Custom Tax Rules\\\" 自定义型\",\r\n        \"operationId\": \"ProductManagement_StoreProductCodeInfo\",\r\n        \"requestBody\": {\r\n          \"x-name\": \"storeCodes\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"type\": \"array\",\r\n                \"items\": {\r\n                  \"$ref\": \"#/components/schemas/KeyValuePairedOfIntegerAndString\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 1\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"$ref\": \"#/components/schemas/ProductInfoDto\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/ProductManagement/product/dept/tax/ids\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"ProductManagement\"\r\n        ],\r\n        \"operationId\": \"ProductManagement_GetDpetBindTaxIds\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"deptId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 2\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"type\": \"string\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/movement/products\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"ProductReport\"\r\n        ],\r\n        \"summary\": \"获取所有店铺正在出售的商品\",\r\n        \"operationId\": \"ProductReport_GetAllStoreProducts\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"StoreArrays\",\r\n            \"in\": \"query\",\r\n            \"style\": \"form\",\r\n            \"explode\": true,\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"nullable\": true,\r\n              \"items\": {\r\n                \"type\": \"integer\",\r\n                \"format\": \"int32\"\r\n              }\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"NonmovementDate\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"format\": \"date-time\"\r\n            },\r\n            \"x-position\": 2\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"404\": {\r\n            \"description\": \"Not Fond\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"200\": {\r\n            \"description\": \"请求成功\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/ProductReportResponse\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/Report/reportToken\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Report\"\r\n        ],\r\n        \"operationId\": \"Report_GetToken\",\r\n        \"requestBody\": {\r\n          \"x-name\": \"requestDto\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/GetTokenRequestDto\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 1\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/Report/discountStoreCoupons\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Report\"\r\n        ],\r\n        \"operationId\": \"Report_GetDiscountStoreCoupons\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeAccessKeys\",\r\n            \"in\": \"query\",\r\n            \"style\": \"form\",\r\n            \"explode\": true,\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"nullable\": true,\r\n              \"items\": {\r\n                \"type\": \"string\"\r\n              }\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"beginDate\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"format\": \"date-time\"\r\n            },\r\n            \"x-position\": 2\r\n          },\r\n          {\r\n            \"name\": \"endDate\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"format\": \"date-time\"\r\n            },\r\n            \"x-position\": 3\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/DiscountStoreCouponResponseDto\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/Report/stores\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Report\"\r\n        ],\r\n        \"operationId\": \"Report_GetStoreList\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"ReportToken\",\r\n            \"x-originalName\": \"token\",\r\n            \"in\": \"header\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/StoreListDto\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/Report/hourSales\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Report\"\r\n        ],\r\n        \"operationId\": \"Report_GetHourSales\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"ReportToken\",\r\n            \"x-originalName\": \"token\",\r\n            \"in\": \"header\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 2\r\n          },\r\n          {\r\n            \"name\": \"beginDate\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"format\": \"date-time\"\r\n            },\r\n            \"x-position\": 3\r\n          },\r\n          {\r\n            \"name\": \"endDate\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"format\": \"date-time\"\r\n            },\r\n            \"x-position\": 4\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/HourlySalesResponseDto\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/Report/departmentGroupSales\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Report\"\r\n        ],\r\n        \"operationId\": \"Report_GetDepartmentGroupSales\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"ReportToken\",\r\n            \"x-originalName\": \"token\",\r\n            \"in\": \"header\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 2\r\n          },\r\n          {\r\n            \"name\": \"beginDate\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"format\": \"date-time\"\r\n            },\r\n            \"x-position\": 3\r\n          },\r\n          {\r\n            \"name\": \"endDate\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"format\": \"date-time\"\r\n            },\r\n            \"x-position\": 4\r\n          },\r\n          {\r\n            \"name\": \"OtherDepartment\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"boolean\",\r\n              \"default\": false\r\n            },\r\n            \"x-position\": 5\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/DepartmentGroupSalesResponseDto\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/Report/tenders\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Report\"\r\n        ],\r\n        \"operationId\": \"Report_GetTenders\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"ReportToken\",\r\n            \"x-originalName\": \"token\",\r\n            \"in\": \"header\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 2\r\n          },\r\n          {\r\n            \"name\": \"beginDate\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"format\": \"date-time\"\r\n            },\r\n            \"x-position\": 3\r\n          },\r\n          {\r\n            \"name\": \"endDate\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"format\": \"date-time\"\r\n            },\r\n            \"x-position\": 4\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/TenderResponse\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/Report/summary\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Report\"\r\n        ],\r\n        \"operationId\": \"Report_GetSummary\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"ReportToken\",\r\n            \"x-originalName\": \"token\",\r\n            \"in\": \"header\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 2\r\n          },\r\n          {\r\n            \"name\": \"beginDate\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"format\": \"date-time\"\r\n            },\r\n            \"x-position\": 3\r\n          },\r\n          {\r\n            \"name\": \"endDate\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"format\": \"date-time\"\r\n            },\r\n            \"x-position\": 4\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/SummaryResponseDto\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/Report/SayHello\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Report\"\r\n        ],\r\n        \"operationId\": \"Report_SayHello\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"name\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/HelloReply2Dto\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/approval/DefaultField\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"SettingConfig\"\r\n        ],\r\n        \"summary\": \"获取默认字段属性\",\r\n        \"description\": \"Get Todo{\\n   \\n  FieldName: (字段名)\\n  \\n  FieldVisibility:         (Show: 可见，         Hide: 隐藏)\\n  \\n  FieldSelection:       (Required : 必选        Optional ：可选)\\n       \\n  }\\n            \\n            \",\r\n        \"operationId\": \"SettingConfig_GetDefaultField\",\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success!\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/SettingConfigResponse\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/approval/ModeifyField/{storeId}\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"SettingConfig\"\r\n        ],\r\n        \"summary\": \"修改属性\",\r\n        \"description\": \"Get Todo{\\n   \\n  FieldName: (字段名)\\n  \\n  FieldVisibility:         (Show: 可见，         Hide: 不可见)\\n  \\n  FieldSelection:       (Required : 必选        Optional ：可选)\\n       \\n  }\\n             \\n             \",\r\n        \"operationId\": \"SettingConfig_PostSettingConfigField\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"description\": \"店铺id\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"requestBody\": {\r\n          \"x-name\": \"config\",\r\n          \"description\": \"属性基本信息\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/SettingConfigResponse\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 2\r\n        },\r\n        \"responses\": {\r\n          \"404\": {\r\n            \"description\": \"Not Found!\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"200\": {\r\n            \"description\": \"Success!\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/approval/GetAllField/{storeId}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"SettingConfig\"\r\n        ],\r\n        \"summary\": \"获取所有字段\",\r\n        \"description\": \"Get Todo{\\n   \\n  FieldName: (字段名)\\n  \\n  FieldVisibility:         (Show: 可见，         Hide: 隐藏)\\n  \\n  FieldSelection:       (Required : 必选        Optional ：可选)\\n       \\n  }\\n             \\n             \",\r\n        \"operationId\": \"SettingConfig_GetFieldName\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"description\": \"店铺id\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success!\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/SettingConfigResponse\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"Not Found!\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/approval\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"SettingConfig\"\r\n        ],\r\n        \"summary\": \"判断storeId 是否存在\",\r\n        \"operationId\": \"SettingConfig_GetStoreId\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success!\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"Not Found!\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/approval/All/SharedTemplate\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"SharedTemplates\"\r\n        ],\r\n        \"summary\": \"获取所有的共享模板 Version 1.6\",\r\n        \"operationId\": \"SharedTemplates_GetSharedTemplate\",\r\n        \"responses\": {\r\n          \"404\": {\r\n            \"description\": \"Not Found!\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"500\": {\r\n            \"description\": \"Time out!\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/ValidationProblemDetails\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"200\": {\r\n            \"description\": \"Success!\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/SharedTemplate\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/approval/Search/Template\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"SharedTemplates\"\r\n        ],\r\n        \"summary\": \"搜索共享库中的模板\",\r\n        \"operationId\": \"SharedTemplates_SearchTemplate\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"templateName\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"guid\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"format\": \"guid\"\r\n            },\r\n            \"x-position\": 2\r\n          },\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 3\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"404\": {\r\n            \"description\": \"Not Found!\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"500\": {\r\n            \"description\": \"Time out!\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/ValidationProblemDetails\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"200\": {\r\n            \"description\": \"Success!\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/SearchTemplate\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/approval/StoreName\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"SharedTemplates\"\r\n        ],\r\n        \"summary\": \"获取商店名\",\r\n        \"operationId\": \"SharedTemplates_GetStoreName\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"500\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/ValidationProblemDetails\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/StoreChainCode/product/code\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"StoreChainCode\"\r\n        ],\r\n        \"summary\": \"根据条件获取指定店铺下的商品条码(在商品条码和描述中只能二选其一)\",\r\n        \"operationId\": \"StoreChainCode_StoreProductCode\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"stores\",\r\n            \"in\": \"query\",\r\n            \"style\": \"form\",\r\n            \"explode\": true,\r\n            \"description\": \"店铺ID\",\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"nullable\": true,\r\n              \"items\": {\r\n                \"type\": \"integer\",\r\n                \"format\": \"int32\"\r\n              }\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"type\",\r\n            \"in\": \"query\",\r\n            \"description\": \"查询类型( None:全部商品 | Normal:普通商品 | Internal:内部商品\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/components/schemas/StoreChainCodeType\"\r\n            },\r\n            \"x-position\": 2\r\n          },\r\n          {\r\n            \"name\": \"isIncludeChainCode\",\r\n            \"in\": \"query\",\r\n            \"description\": \"是否包含供应链码(True: 只返回供应链码不为空 | False: 只返回供应链码为空 | null: 返回所有)\",\r\n            \"schema\": {\r\n              \"type\": \"boolean\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 3\r\n          },\r\n          {\r\n            \"name\": \"barcode\",\r\n            \"in\": \"query\",\r\n            \"description\": \"商品条码\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 4\r\n          },\r\n          {\r\n            \"name\": \"description\",\r\n            \"in\": \"query\",\r\n            \"description\": \"商品描述\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 5\r\n          },\r\n          {\r\n            \"name\": \"department\",\r\n            \"in\": \"query\",\r\n            \"description\": \"部门描述\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 6\r\n          },\r\n          {\r\n            \"name\": \"filterSharedProducts\",\r\n            \"in\": \"query\",\r\n            \"description\": \"是否过滤出多家店铺共同拥有的商品(True: 过滤 | False： 不过滤)\",\r\n            \"schema\": {\r\n              \"type\": \"boolean\",\r\n              \"default\": false\r\n            },\r\n            \"x-position\": 7\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"店铺条码数组\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"$ref\": \"#/components/schemas/KeyValuePairedOfIntegerAndString\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/StoreChainCode/product/code/info\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"StoreChainCode\"\r\n        ],\r\n        \"summary\": \"根据条形码获取指定店铺下的商品信息\",\r\n        \"operationId\": \"StoreChainCode_StoreProductCodeInfo\",\r\n        \"requestBody\": {\r\n          \"x-name\": \"storeCodes\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"type\": \"array\",\r\n                \"items\": {\r\n                  \"$ref\": \"#/components/schemas/KeyValuePairedOfIntegerAndString\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 1\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"$ref\": \"#/components/schemas/StoreChainCodeInfo\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"put\": {\r\n        \"tags\": [\r\n          \"StoreChainCode\"\r\n        ],\r\n        \"summary\": \"更新多家店铺中内部商品的唯一内部条码\",\r\n        \"operationId\": \"StoreChainCode_UpdateProductInfo\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"isUpdatePLuEnabled\",\r\n            \"in\": \"query\",\r\n            \"description\": \"是否启用更新PLU表中这件商品本身(Desc|Department)\",\r\n            \"schema\": {\r\n              \"type\": \"boolean\"\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"requestBody\": {\r\n          \"x-name\": \"chainCodeDto\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/UpdateProductChainCodeDto\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 2\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/StoreChainCode/product/code/update/is-permitted\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"StoreChainCode\"\r\n        ],\r\n        \"summary\": \"是否允许更新\",\r\n        \"operationId\": \"StoreChainCode_IsUpdatePermitted\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"stores\",\r\n            \"in\": \"query\",\r\n            \"style\": \"form\",\r\n            \"explode\": true,\r\n            \"description\": \"店铺\",\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"nullable\": true,\r\n              \"items\": {\r\n                \"type\": \"integer\",\r\n                \"format\": \"int32\"\r\n              }\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"department\",\r\n            \"in\": \"query\",\r\n            \"description\": \"部门\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 2\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"True：允许更新 | False：不允许更新\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"$ref\": \"#/components/schemas/KeyValuePairedOfIntegerAndBoolean\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/StoreDataClone/clone/product\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"StoreDataClone\"\r\n        ],\r\n        \"summary\": \"克隆商品数据\",\r\n        \"description\": \"指定复制的商品条码列表/指定部门(当指定部门，默认将当前部门下的所有商品复制到另一家店铺)\",\r\n        \"operationId\": \"StoreDataClone_ProductClone\",\r\n        \"requestBody\": {\r\n          \"x-name\": \"dto\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/StoreDataCloneBody\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 1\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/StoreDataClone/clone/product/attribute\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"StoreDataClone\"\r\n        ],\r\n        \"summary\": \"克隆商品属性\",\r\n        \"description\": \"指定复制的商品条码列表/指定部门(当指定部门，默认将当前部门下的所有商品复制到另一家店铺)\",\r\n        \"operationId\": \"StoreDataClone_ProductAttributeClone\",\r\n        \"requestBody\": {\r\n          \"x-name\": \"dto\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/StoreDataCloneBody\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 1\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/StoreDataClone/clone/mutibarcode\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"StoreDataClone\"\r\n        ],\r\n        \"operationId\": \"StoreDataClone_MutiBarcodeClone\",\r\n        \"requestBody\": {\r\n          \"x-name\": \"cloneBody\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/MutiBarcodeCloneBody\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 1\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/StoreDataClone/clone/barcodes\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"StoreDataClone\"\r\n        ],\r\n        \"operationId\": \"StoreDataClone_GetBarCode\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"departments\",\r\n            \"in\": \"query\",\r\n            \"style\": \"form\",\r\n            \"explode\": true,\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"nullable\": true,\r\n              \"items\": {\r\n                \"type\": \"string\"\r\n              }\r\n            },\r\n            \"x-position\": 2\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"type\": \"string\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/StoreInventory/barCodeList\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"StoreInventory\"\r\n        ],\r\n        \"summary\": \"多家店铺：搜索商品条码\",\r\n        \"operationId\": \"StoreInventory_GetBarCodeList\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"StoreIdList\",\r\n            \"in\": \"query\",\r\n            \"style\": \"form\",\r\n            \"explode\": true,\r\n            \"description\": \"店铺ID列表\",\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"nullable\": true,\r\n              \"items\": {\r\n                \"type\": \"integer\",\r\n                \"format\": \"int32\"\r\n              }\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"SearchType\",\r\n            \"in\": \"query\",\r\n            \"description\": \"搜索类型 (Text / UPC)\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/components/schemas/SearchType\"\r\n            },\r\n            \"x-position\": 2\r\n          },\r\n          {\r\n            \"name\": \"brand\",\r\n            \"in\": \"query\",\r\n            \"description\": \"品牌\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 3\r\n          },\r\n          {\r\n            \"name\": \"vendor\",\r\n            \"in\": \"query\",\r\n            \"description\": \"供货商\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 4\r\n          },\r\n          {\r\n            \"name\": \"department\",\r\n            \"in\": \"query\",\r\n            \"description\": \"部门\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 5\r\n          },\r\n          {\r\n            \"name\": \"SearchText\",\r\n            \"in\": \"query\",\r\n            \"description\": \"搜索内容\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 6\r\n          },\r\n          {\r\n            \"name\": \"isVisible\",\r\n            \"in\": \"query\",\r\n            \"description\": \"只返回库存(>=0)的商品(Default: False)\",\r\n            \"schema\": {\r\n              \"type\": \"boolean\",\r\n              \"default\": false\r\n            },\r\n            \"x-position\": 7\r\n          },\r\n          {\r\n            \"name\": \"minInventory\",\r\n            \"in\": \"query\",\r\n            \"description\": \"最小库存数量(Default: null)\",\r\n            \"schema\": {\r\n              \"type\": \"number\",\r\n              \"format\": \"decimal\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 8\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"type\": \"string\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/StoreInventory/inventories\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"StoreInventory\"\r\n        ],\r\n        \"summary\": \"多家店铺：商品库存信息\",\r\n        \"operationId\": \"StoreInventory_GetStoreInventorys\",\r\n        \"requestBody\": {\r\n          \"x-name\": \"request\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/SearchStoreInventroyRequest\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 1\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"$ref\": \"#/components/schemas/StoreInventoryDto\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/StoreInventory/transferRequest\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"StoreInventory\"\r\n        ],\r\n        \"summary\": \"获取所有商品转移请求订单\",\r\n        \"operationId\": \"StoreInventory_GetAllTransferRequest\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeIdList\",\r\n            \"in\": \"query\",\r\n            \"style\": \"form\",\r\n            \"explode\": true,\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"nullable\": true,\r\n              \"items\": {\r\n                \"type\": \"integer\",\r\n                \"format\": \"int32\"\r\n              }\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"queryDays\",\r\n            \"in\": \"query\",\r\n            \"description\": \"查询天数范围(默认30天)\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\",\r\n              \"default\": 30\r\n            },\r\n            \"x-position\": 2\r\n          },\r\n          {\r\n            \"name\": \"isReceived\",\r\n            \"in\": \"query\",\r\n            \"description\": \"是否包含已收到的请求(默认不包含)\",\r\n            \"schema\": {\r\n              \"type\": \"boolean\",\r\n              \"default\": false\r\n            },\r\n            \"x-position\": 3\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"$ref\": \"#/components/schemas/GetTransferResponseDto\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"StoreInventory\"\r\n        ],\r\n        \"summary\": \"创建商品转移请求订单\",\r\n        \"operationId\": \"StoreInventory_CreateTransferRequest\",\r\n        \"requestBody\": {\r\n          \"x-name\": \"createTransferRequest\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/CreateTransferRequestDto\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 1\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"guid\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"delete\": {\r\n        \"tags\": [\r\n          \"StoreInventory\"\r\n        ],\r\n        \"summary\": \"删除多个商品转移请求订单\",\r\n        \"operationId\": \"StoreInventory_DeleteTransferRequest\",\r\n        \"requestBody\": {\r\n          \"x-name\": \"transferRequestIdList\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"type\": \"array\",\r\n                \"items\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"guid\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 1\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/StoreInventory/transferRequest/search\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"StoreInventory\"\r\n        ],\r\n        \"summary\": \"搜索转移请求订单\",\r\n        \"operationId\": \"StoreInventory_SearchTransferRequest\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"requestId\",\r\n            \"in\": \"query\",\r\n            \"description\": \"订单id\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"format\": \"guid\"\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/GetTransferResponseDto\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/StoreInventory/transferRequest/status\": {\r\n      \"put\": {\r\n        \"tags\": [\r\n          \"StoreInventory\"\r\n        ],\r\n        \"summary\": \"更新转移请求订单状态\",\r\n        \"operationId\": \"StoreInventory_UpdateTransferRequestStatus\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"transferRequestId\",\r\n            \"in\": \"query\",\r\n            \"description\": \"转移请求ID\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"format\": \"guid\"\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"status\",\r\n            \"in\": \"query\",\r\n            \"description\": \"订单状态\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/components/schemas/TransferStatus\"\r\n            },\r\n            \"x-position\": 2\r\n          },\r\n          {\r\n            \"name\": \"userId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\",\r\n              \"default\": 101\r\n            },\r\n            \"x-position\": 3\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/GetTransferResponseDto\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/StoreInventory/transferRequest/update-store-warehouse-inventory\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"StoreInventory\"\r\n        ],\r\n        \"summary\": \"更新商品仓库的库存\",\r\n        \"operationId\": \"StoreInventory_UpdateStoreInvertory\",\r\n        \"requestBody\": {\r\n          \"x-name\": \"bodyDto\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/UpdateStoreInventoryDto\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 1\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/StoreInventory/transferRequest/products\": {\r\n      \"put\": {\r\n        \"tags\": [\r\n          \"StoreInventory\"\r\n        ],\r\n        \"summary\": \"更新商品转移请求订单中的商品数据\",\r\n        \"operationId\": \"StoreInventory_UpdateTransferProductInfo\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"transferRequestID\",\r\n            \"in\": \"query\",\r\n            \"description\": \"转移请求ID\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"format\": \"guid\"\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"requestBody\": {\r\n          \"x-name\": \"productInfos\",\r\n          \"description\": \"商品信息列表\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"type\": \"array\",\r\n                \"items\": {\r\n                  \"$ref\": \"#/components/schemas/TransferProductInfo\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 2\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/StoreInventory/transferRequest/products/receive/qty\": {\r\n      \"put\": {\r\n        \"tags\": [\r\n          \"StoreInventory\"\r\n        ],\r\n        \"summary\": \"更新转移请求中商品实际收到的货品数量\",\r\n        \"operationId\": \"StoreInventory_UpdateTransferProductReceivedQty\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"transferRequestID\",\r\n            \"in\": \"query\",\r\n            \"description\": \"转移请求ID\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"format\": \"guid\"\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"barCode\",\r\n            \"in\": \"query\",\r\n            \"description\": \"当前订单中的商品条码\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 2\r\n          },\r\n          {\r\n            \"name\": \"receiveQty\",\r\n            \"in\": \"query\",\r\n            \"description\": \"商品实际收到的数量\",\r\n            \"schema\": {\r\n              \"type\": \"number\",\r\n              \"format\": \"decimal\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 3\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/StoreReport/department/CompaeisonReport\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"StoreReport\"\r\n        ],\r\n        \"summary\": \"部门数据比较（按照组比较并且查询）\",\r\n        \"operationId\": \"StoreReport_GetDepartmentCompaeison\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"reportType\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/components/schemas/ReportType\"\r\n            },\r\n            \"x-position\": 2\r\n          }\r\n        ],\r\n        \"requestBody\": {\r\n          \"x-name\": \"body\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/DepartmentBodys\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 1\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"$ref\": \"#/components/schemas/DepartmentDifferenceReportDto\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/StoreReport/group/CompaeisonReport\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"StoreReport\"\r\n        ],\r\n        \"summary\": \"分组总和差异化报表\",\r\n        \"operationId\": \"StoreReport_GetGroupCompaeisonReport\",\r\n        \"requestBody\": {\r\n          \"x-name\": \"body\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/DepartmentBodys\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 1\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/GroupDifferenceReportDto\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/StoreReport/store/CompaeisonReport\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"StoreReport\"\r\n        ],\r\n        \"summary\": \"店铺数据报表\",\r\n        \"operationId\": \"StoreReport_GetStoreGroupComparison\",\r\n        \"requestBody\": {\r\n          \"x-name\": \"body\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/DepartmentBody\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 1\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"$ref\": \"#/components/schemas/StoreGroupComparisonDto\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/StoreSalesRanking\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"StoreSalesRanking\"\r\n        ],\r\n        \"summary\": \"获取店铺商品销售榜\",\r\n        \"operationId\": \"StoreSalesRanking_GetSalesRanking\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"stores\",\r\n            \"in\": \"query\",\r\n            \"style\": \"form\",\r\n            \"explode\": true,\r\n            \"description\": \"店铺\",\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"nullable\": true,\r\n              \"items\": {\r\n                \"type\": \"integer\",\r\n                \"format\": \"int32\"\r\n              }\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"begin\",\r\n            \"in\": \"query\",\r\n            \"description\": \"起始\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 2\r\n          },\r\n          {\r\n            \"name\": \"end\",\r\n            \"in\": \"query\",\r\n            \"description\": \"结束\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 3\r\n          },\r\n          {\r\n            \"name\": \"salesRankingCount\",\r\n            \"in\": \"query\",\r\n            \"description\": \"榜单数\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 4\r\n          },\r\n          {\r\n            \"name\": \"department\",\r\n            \"in\": \"query\",\r\n            \"description\": \"部门\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 5\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"$ref\": \"#/components/schemas/SalesRankingDto\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/Stores\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Stores\"\r\n        ],\r\n        \"summary\": \"Created Stores\",\r\n        \"operationId\": \"Stores_CreatedStore\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"companyId\",\r\n            \"in\": \"query\",\r\n            \"description\": \"Company Id\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"requestBody\": {\r\n          \"x-name\": \"storesDTO\",\r\n          \"description\": \"Stores\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/StoresDTO\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 2\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Created Success!\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/StoresDTO\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"409\": {\r\n            \"description\": \"StoreName已经存在或者DataMinerAccessKey已经存在\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"400\": {\r\n            \"description\": \"传递的参数中包含重复的StoreName或者重复的DataMinerAccessKey\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        },\r\n        \"security\": [\r\n          {\r\n            \"Bearer\": [\r\n              \"administrator\"\r\n            ]\r\n          }\r\n        ]\r\n      },\r\n      \"delete\": {\r\n        \"tags\": [\r\n          \"Stores\"\r\n        ],\r\n        \"summary\": \"Delete Stores\",\r\n        \"operationId\": \"Stores_DeleteStore\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"companyId\",\r\n            \"in\": \"query\",\r\n            \"description\": \"Company Id\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"requestBody\": {\r\n          \"x-name\": \"storesId\",\r\n          \"description\": \"Store Id List\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"type\": \"array\",\r\n                \"items\": {\r\n                  \"type\": \"integer\",\r\n                  \"format\": \"int32\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 2\r\n        },\r\n        \"responses\": {\r\n          \"204\": {\r\n            \"description\": \"Delete Success!\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"409\": {\r\n            \"description\": \"Delete Failure!\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"Not Found!\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        },\r\n        \"security\": [\r\n          {\r\n            \"Bearer\": [\r\n              \"administrator\"\r\n            ]\r\n          }\r\n        ]\r\n      },\r\n      \"put\": {\r\n        \"tags\": [\r\n          \"Stores\"\r\n        ],\r\n        \"summary\": \"Update Store\",\r\n        \"operationId\": \"Stores_UpdateStore\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"companyId\",\r\n            \"in\": \"query\",\r\n            \"description\": \"Company Id\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"requestBody\": {\r\n          \"x-name\": \"storeDTO\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/StoreDTO\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 2\r\n        },\r\n        \"responses\": {\r\n          \"204\": {\r\n            \"description\": \"Update Success!\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"409\": {\r\n            \"description\": \"修改的店铺内容包含已经存在的!\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"Not Found!\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"400\": {\r\n            \"description\": \"传递的参数中包含重复的StoreName或者重复的DataMinerAccessKey!\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        },\r\n        \"security\": [\r\n          {\r\n            \"Bearer\": [\r\n              \"administrator\"\r\n            ]\r\n          }\r\n        ]\r\n      },\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Stores\"\r\n        ],\r\n        \"summary\": \"Get Stores\",\r\n        \"operationId\": \"Stores_GetCompanyStore\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"companyId\",\r\n            \"in\": \"query\",\r\n            \"description\": \"Company Id\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"204\": {\r\n            \"description\": \"请求成功，但当前公司旗下没有店铺\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"200\": {\r\n            \"description\": \"请求成功，返回店铺集合\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/StoresDTO\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"Not Found!\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/Stores/details\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Stores\"\r\n        ],\r\n        \"operationId\": \"Stores_GetStoreId\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"encryptedId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 2\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/StoreDTO\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/approval/{storeId}/{userId}/{eventId}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"TemporaryToken\"\r\n        ],\r\n        \"summary\": \"生成带有时效性的Token\",\r\n        \"operationId\": \"TemporaryToken_GetTemporaryToken\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"storeId\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"description\": \"商店ID\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"userId\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"description\": \"用户ID\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 2\r\n          },\r\n          {\r\n            \"name\": \"eventId\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"description\": \"事件ID\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 3\r\n          },\r\n          {\r\n            \"name\": \"devmode\",\r\n            \"in\": \"query\",\r\n            \"description\": \"模式{true：开发模式 false：生产模式}\",\r\n            \"schema\": {\r\n              \"type\": \"boolean\"\r\n            },\r\n            \"x-position\": 4\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"302\": {\r\n            \"description\": \"重定向\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"Not Found!\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/approval/eventContent/{temporarytoken}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"TemporaryToken\"\r\n        ],\r\n        \"summary\": \"验证Token和Token的时效性\",\r\n        \"description\": \"Sample request:\\n            \\n    GET /Todo\\n    {\\n       \\n        EventId:                   事件id\\n        \\n        EventDesc:                 描述\\n        \\n        EventDate:                 事件日期\\n        \\n        Approved:                  当前状态   Un_Authorized：等待授权，\\n                                             Authorized：授权，\\n                                             Deny_Authorization：拒绝授权，\\n                                             Cancel_Authorization：取消授权\\n                                              \\n        ApproverById:              授权经理Id\\n        \\n        ApprovedByName:            授权经理名字\\n        \\n        JRCashierId:               收银员ID\\n        \\n        JRCashierName:             收银员名字\\n        \\n        NeededManagerLevel:        要求权限级别\\n        \\n        JRTRX:                     订单号\\n        \\n        Reg:                       收银台号\\n    }\\n            \",\r\n        \"operationId\": \"TemporaryToken_GetEventContent\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"temporarytoken\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"description\": \"token\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"返回事件内容\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/EventResponse\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"token为空或Token不存在\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"401\": {\r\n            \"description\": \"token 过期\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/ValidationProblemDetails\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/approval/response/{temporarytoken}\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"TemporaryToken\"\r\n        ],\r\n        \"summary\": \"授权\",\r\n        \"description\": \"Sample request:\\n            \\n    POST /Todo\\n    {\\n       \\n        EventId:                   事件id\\n        \\n        EventDesc:                 描述\\n        \\n        EventDate:                 事件日期\\n        \\n        Approved:                  当前状态   Un_Authorized：等待授权，\\n                                             Authorized：授权，\\n                                             Deny_Authorization：拒绝授权，\\n                                             Cancel_Authorization：取消授权\\n                                              \\n        ApproverById:              授权经理Id\\n        \\n        ApprovedByName:            授权经理名字\\n        \\n        JRCashierId:               收银员ID\\n        \\n        JRCashierName:             收银员名字\\n        \\n        NeededManagerLevel:        要求权限级别\\n        \\n        JRTRX:                     订单号\\n        \\n        Reg:                       收银台号\\n            \",\r\n        \"operationId\": \"TemporaryToken_PostAuthorize\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"temporarytoken\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"description\": \"token\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          },\r\n          {\r\n            \"name\": \"eventApproved\",\r\n            \"in\": \"query\",\r\n            \"description\": \"状态\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/components/schemas/EventApproved\"\r\n            },\r\n            \"x-position\": 2\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"409\": {\r\n            \"description\": \"并发冲突\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/ValidationProblemDetails\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"404\": {\r\n            \"description\": \"token无效或没有传入token\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"401\": {\r\n            \"description\": \"token过期\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/ValidationProblemDetails\"\r\n                }\r\n              }\r\n            }\r\n          },\r\n          \"200\": {\r\n            \"description\": \"Token验证成功\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"$ref\": \"#/components/schemas/EventResponse\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/api/User\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"User\"\r\n        ],\r\n        \"operationId\": \"User_GetUsers\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"companyName\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"string\",\r\n              \"nullable\": true\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"array\",\r\n                  \"items\": {\r\n                    \"$ref\": \"#/components/schemas/UsersDto\"\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"put\": {\r\n        \"tags\": [\r\n          \"User\"\r\n        ],\r\n        \"operationId\": \"User_UpdateUser\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"userId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"requestBody\": {\r\n          \"x-name\": \"newUser\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"$ref\": \"#/components/schemas/UpdateUser\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 2\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        },\r\n        \"security\": [\r\n          {\r\n            \"Bearer\": [\r\n              \"administrator\"\r\n            ]\r\n          }\r\n        ]\r\n      },\r\n      \"delete\": {\r\n        \"tags\": [\r\n          \"User\"\r\n        ],\r\n        \"operationId\": \"User_DeleteUser\",\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"userId\",\r\n            \"in\": \"query\",\r\n            \"schema\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"x-position\": 1\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/octet-stream\": {\r\n                \"schema\": {\r\n                  \"type\": \"string\",\r\n                  \"format\": \"binary\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        },\r\n        \"security\": [\r\n          {\r\n            \"Bearer\": [\r\n              \"administrator\"\r\n            ]\r\n          }\r\n        ]\r\n      }\r\n    },\r\n    \"/api/User/register\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"User\"\r\n        ],\r\n        \"summary\": \"注册用户接口\",\r\n        \"description\": \"请求数据结构:\\n```json\\n{\\n    \\\"UserName\\\": \\\"string\\\",       // 用户邮箱\\n    \\\"Password\\\": \\\"string\\\",    // 用户密码\\n    \\\"CompanyName\\\": \\\"string\\\", // 公司名称\\n    \\\"Stores\\\": \\\"string\\\"       // 店铺(每个店铺ID以逗号可开，例如：\\\"1,2,3\\\")（可选）\\n}\\n```\",\r\n        \"operationId\": \"User_RegisterUser\",\r\n        \"requestBody\": {\r\n          \"x-name\": \"obfuscatedDto\",\r\n          \"description\": \"混淆后的注册信息(传入时先转换JSON，再转换成Base64)\",\r\n          \"content\": {\r\n            \"application/json\": {\r\n              \"schema\": {\r\n                \"type\": \"string\"\r\n              }\r\n            }\r\n          },\r\n          \"required\": true,\r\n          \"x-position\": 1\r\n        },\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"\",\r\n            \"content\": {\r\n              \"application/json\": {\r\n                \"schema\": {\r\n                  \"type\": \"integer\",\r\n                  \"format\": \"int32\"\r\n                }\r\n              }\r\n            }\r\n          }\r\n        },\r\n        \"security\": [\r\n          {\r\n            \"Bearer\": [\r\n              \"administrator\"\r\n            ]\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  \"components\": {\r\n    \"schemas\": {\r\n      \"Discount\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"code\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"storeName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"total\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\"\r\n          },\r\n          \"qty\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"description\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"StoreConnection\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"storeId\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"storeName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"commpanyName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"created\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\"\r\n          },\r\n          \"status\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"accessKey\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"Department\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"id\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"departmentDesc\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"Vendor\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"id\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\"\r\n          },\r\n          \"vendorDesc\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"Brand\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"id\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"brandDesc\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"TaxDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"id\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"percentage\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\",\r\n            \"nullable\": true\r\n          },\r\n          \"descript\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"DepartmentGroupDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"id\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\",\r\n            \"nullable\": true\r\n          },\r\n          \"description\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"BundlesDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"id\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\"\r\n          },\r\n          \"description\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"beginDate\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\"\r\n          },\r\n          \"endDate\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\"\r\n          },\r\n          \"forceBundleTotal\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"CompanyDTO\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"id\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"companyName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"CompaniesDTO\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"companies\": {\r\n            \"type\": \"array\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/CompanyDTO\"\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"AccountNumberDetailDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"id\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"accountNumber\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"firstName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"lastName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"email\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"phoneNumber\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"telPhone\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"address\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"city\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"state\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"zipCode\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"createdDate\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\",\r\n            \"nullable\": true\r\n          },\r\n          \"lastVisit\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\",\r\n            \"nullable\": true\r\n          },\r\n          \"lastVisitStore\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"inactive\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\",\r\n            \"nullable\": true\r\n          },\r\n          \"mergedTo\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"customerLevel\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\",\r\n            \"nullable\": true\r\n          },\r\n          \"loyaltyEXCLUDE\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\",\r\n            \"nullable\": true\r\n          },\r\n          \"managerNeededForThisCustomer\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\",\r\n            \"nullable\": true\r\n          },\r\n          \"memo\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"oneTimeDiscount\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\",\r\n            \"nullable\": true\r\n          },\r\n          \"taxExPt\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\",\r\n            \"nullable\": true\r\n          },\r\n          \"birthday\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\",\r\n            \"nullable\": true\r\n          },\r\n          \"pointBlance\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\"\r\n          },\r\n          \"yearToDateSales\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"AccountNumberDetailsBody\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"companyId\",\r\n          \"accountNumbers\"\r\n        ],\r\n        \"properties\": {\r\n          \"companyId\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"accountNumbers\": {\r\n            \"type\": \"array\",\r\n            \"items\": {\r\n              \"type\": \"string\"\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"FoodCategoryDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"id\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\"\r\n          },\r\n          \"name\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"stortOrder\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"parentId\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\",\r\n            \"nullable\": true\r\n          },\r\n          \"createDate\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"InsertFoodCategoryBody\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"id\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\"\r\n          },\r\n          \"name\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"stortOrder\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"parentId\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"UpdateFoodCategoryBody\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"id\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\"\r\n          },\r\n          \"name\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"UpdateSortOrdersBody\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"sortOrderItems\": {\r\n            \"type\": \"array\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/SortOrderItem\"\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"SortOrderItem\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"id\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\"\r\n          },\r\n          \"sortOrder\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          }\r\n        }\r\n      },\r\n      \"FoodItemDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"id\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\"\r\n          },\r\n          \"foodCategory_ID\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\",\r\n            \"nullable\": true\r\n          },\r\n          \"productDetails\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"foodImagePath\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"plU_ID\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\",\r\n            \"nullable\": true\r\n          },\r\n          \"barcode\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"createDate\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\",\r\n            \"nullable\": true\r\n          },\r\n          \"description\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"price\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\",\r\n            \"nullable\": true\r\n          },\r\n          \"priceBase\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\",\r\n            \"nullable\": true\r\n          },\r\n          \"sortOrder\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"FoodItemDetailsDto\": {\r\n        \"type\": \"object\",\r\n        \"description\": \"FoodItem详细信息\\n \",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"item\": {\r\n            \"description\": \"商品基本信息\",\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/FoodItemDto\"\r\n              }\r\n            ]\r\n          },\r\n          \"imageGallery\": {\r\n            \"type\": \"array\",\r\n            \"description\": \"商品副图数组\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"type\": \"string\"\r\n            }\r\n          },\r\n          \"attributeGroup\": {\r\n            \"type\": \"array\",\r\n            \"description\": \"商品属性组列表\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/ProductAttributeGroupDto\"\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"ProductAttributeGroupDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"id\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"组ID\",\r\n            \"format\": \"guid\"\r\n          },\r\n          \"plU_ID\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"商品ID\",\r\n            \"format\": \"guid\",\r\n            \"nullable\": true\r\n          },\r\n          \"description\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"组描述\",\r\n            \"nullable\": true\r\n          },\r\n          \"maximumSelected\": {\r\n            \"type\": \"integer\",\r\n            \"description\": \"最大选择数量\",\r\n            \"format\": \"int32\",\r\n            \"nullable\": true\r\n          },\r\n          \"minimumSelected\": {\r\n            \"type\": \"integer\",\r\n            \"description\": \"最小选择数量\",\r\n            \"format\": \"int32\",\r\n            \"nullable\": true\r\n          },\r\n          \"items\": {\r\n            \"type\": \"array\",\r\n            \"description\": \"组成员列表\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/ProductAttributeItemDto\"\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"ProductAttributeItemDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"id\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\"\r\n          },\r\n          \"name\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"price\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\",\r\n            \"nullable\": true\r\n          },\r\n          \"productAttributeGroup_ID\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\"\r\n          },\r\n          \"color\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"displayIndex\": {\r\n            \"type\": \"number\",\r\n            \"description\": \"显示位置\",\r\n            \"format\": \"decimal\",\r\n            \"nullable\": true\r\n          },\r\n          \"checkedByDefault\": {\r\n            \"type\": \"integer\",\r\n            \"description\": \"默认当前是否选中\",\r\n            \"format\": \"int32\",\r\n            \"nullable\": true\r\n          },\r\n          \"instruction\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"InserFoodItemBody\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"id\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\"\r\n          },\r\n          \"foodCategory_ID\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\",\r\n            \"nullable\": true\r\n          },\r\n          \"productDetails\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"foodImagePath\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"barcode\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"sortOrder\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"UpdateFoodItemBody\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"pluId\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\",\r\n            \"nullable\": true\r\n          },\r\n          \"productDetails\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"foodImagePath\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"foodCategory_ID\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"ItemBasicInfoDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"id\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\",\r\n            \"nullable\": true\r\n          },\r\n          \"barcode\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"pluId\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\",\r\n            \"nullable\": true\r\n          },\r\n          \"description\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"deatils\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"imagePath\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"FoodImageChildDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"id\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\"\r\n          },\r\n          \"imagePath\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"sortOrder\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"ImageItem\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"parent_ID\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\"\r\n          },\r\n          \"id\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\"\r\n          },\r\n          \"imagePath\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"sortOrder\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"Device\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"id\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"name\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"stationNumber\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"storeName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"storeId\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"DeviceRecord\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"id\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int64\"\r\n          },\r\n          \"beginTime\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\"\r\n          },\r\n          \"endTime\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\"\r\n          },\r\n          \"link\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"deviceId\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"SupplierType\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"ASGHQ\"\r\n        ],\r\n        \"enum\": [\r\n          \"ASGHQ\"\r\n        ]\r\n      },\r\n      \"TransferMixcodeBodyDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"currentStore\",\r\n          \"targentStore\"\r\n        ],\r\n        \"properties\": {\r\n          \"currentStore\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"targentStore\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"mixcodes\": {\r\n            \"type\": \"array\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"type\": \"string\"\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"AVSResponse\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"response\",\r\n          \"nbInternalTrace\"\r\n        ],\r\n        \"properties\": {\r\n          \"response\": {\r\n            \"$ref\": \"#/components/schemas/Response5\"\r\n          },\r\n          \"nbInternalTrace\": {\r\n            \"$ref\": \"#/components/schemas/NationsInternalTrace\"\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"Response5\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"memberIDs\": {\r\n            \"type\": \"array\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/Member\"\r\n            }\r\n          },\r\n          \"status\": {\r\n            \"description\": \"Additional status information in human readable form\",\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/Response5Status\"\r\n              }\r\n            ]\r\n          },\r\n          \"code\": {\r\n            \"description\": \"Additional status information\",\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/Response5Code\"\r\n              }\r\n            ]\r\n          },\r\n          \"details\": {\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/ExceptionDetails\"\r\n              }\r\n            ]\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"Member\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"uidType\",\r\n          \"uid\"\r\n        ],\r\n        \"properties\": {\r\n          \"uidType\": {\r\n            \"description\": \"The type of identifier for the member.  Note that the panHashId and trackOneId must be in SHA-256 UTF-8 Format.\",\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/MemberUidType\"\r\n              }\r\n            ]\r\n          },\r\n          \"uid\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"The unique NationsBenefits identifier for the member.\"\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"MemberUidType\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"PanHashId\",\r\n          \"TrackTwoId\",\r\n          \"TrackOneId\",\r\n          \"KeyFobId\",\r\n          \"QrCodeId\"\r\n        ],\r\n        \"enum\": [\r\n          \"panHashId\",\r\n          \"trackTwoId\",\r\n          \"trackOneId\",\r\n          \"keyFobId\",\r\n          \"qrCodeId\"\r\n        ]\r\n      },\r\n      \"Response5Status\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"Account_validation_successful\",\r\n          \"Card_reported_lost_or_stolen\",\r\n          \"Member_not_found\",\r\n          \"Merchant_not_found\",\r\n          \"Missing_or_incorrect_data_provided_to_complete_the_validation_request\",\r\n          \"Card_not_activated\",\r\n          \"Fraud_suspected\",\r\n          \"Provided_CVV_incorrect\",\r\n          \"Provided_expiration_date_incorrect\",\r\n          \"Provided_additional_info_incorrect\",\r\n          \"No_active_purses_found_for_this_member\",\r\n          \"Card_not_eligible_to_add_to_wallet\",\r\n          \"General_exception\"\r\n        ],\r\n        \"enum\": [\r\n          \"Account validation successful\",\r\n          \"Card reported lost or stolen\",\r\n          \"Member not found\",\r\n          \"Merchant not found\",\r\n          \"Missing or incorrect data provided to complete the validation request\",\r\n          \"Card not activated\",\r\n          \"Fraud suspected\",\r\n          \"Provided CVV incorrect\",\r\n          \"Provided expiration date incorrect\",\r\n          \"Provided additional info incorrect\",\r\n          \"No active purses found for this member\",\r\n          \"Card not eligible to add to wallet\",\r\n          \"General exception\"\r\n        ]\r\n      },\r\n      \"Response5Code\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"_000\",\r\n          \"_201\",\r\n          \"_202\",\r\n          \"_203\",\r\n          \"_204\",\r\n          \"_205\",\r\n          \"_206\",\r\n          \"_207\",\r\n          \"_208\",\r\n          \"_209\",\r\n          \"_210\",\r\n          \"_211\",\r\n          \"_300\"\r\n        ],\r\n        \"enum\": [\r\n          \"000\",\r\n          \"201\",\r\n          \"202\",\r\n          \"203\",\r\n          \"204\",\r\n          \"205\",\r\n          \"206\",\r\n          \"207\",\r\n          \"208\",\r\n          \"209\",\r\n          \"210\",\r\n          \"211\",\r\n          \"300\"\r\n        ]\r\n      },\r\n      \"ExceptionDetails\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"propertyName\",\r\n          \"errorMessage\"\r\n        ],\r\n        \"properties\": {\r\n          \"propertyName\": {\r\n            \"description\": \"Message element experiencing an error condition\"\r\n          },\r\n          \"errorMessage\": {\r\n            \"description\": \"Reason that message element is experiencing an error condition\"\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"NationsInternalTrace\": {\r\n        \"type\": \"object\",\r\n        \"description\": \"Object for internal NationsBenefits for developer and debugging purposes\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"statusCode\",\r\n          \"traceId\",\r\n          \"traceIdDate\"\r\n        ],\r\n        \"properties\": {\r\n          \"statusCode\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"Nations Internal Code used for debugging.\"\r\n          },\r\n          \"traceId\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"A unique ID generated by Nations for tracing.\"\r\n          },\r\n          \"traceIdDate\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\"\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"AVSRequest\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"pan\",\r\n          \"cvv\",\r\n          \"expiration\",\r\n          \"additionalInfo\",\r\n          \"merchant\"\r\n        ],\r\n        \"properties\": {\r\n          \"pan\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"Card PAN in SHA-256 UTF-8 Format\"\r\n          },\r\n          \"cvv\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"Card CVV in SHA-256 UTF-8 Format\"\r\n          },\r\n          \"expiration\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"Expiration date from card in format MM/YY\"\r\n          },\r\n          \"additionalInfo\": {\r\n            \"type\": \"array\",\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/AdditionalAVSData\"\r\n            }\r\n          },\r\n          \"entryMode\": {\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/AVSRequestEntryMode\"\r\n              }\r\n            ]\r\n          },\r\n          \"merchant\": {\r\n            \"$ref\": \"#/components/schemas/Merchant\"\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"AdditionalAVSData\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"dataType\",\r\n          \"value\"\r\n        ],\r\n        \"properties\": {\r\n          \"dataType\": {\r\n            \"$ref\": \"#/components/schemas/AdditionalAVSDataDataType\"\r\n          },\r\n          \"value\": {\r\n            \"type\": \"string\"\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"AdditionalAVSDataDataType\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"BillingZipCode\",\r\n          \"DateOfBirth\"\r\n        ],\r\n        \"enum\": [\r\n          \"billingZipCode\",\r\n          \"dateOfBirth\"\r\n        ]\r\n      },\r\n      \"AVSRequestEntryMode\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"Swipe\",\r\n          \"Dip\",\r\n          \"Tap\",\r\n          \"Keyed\",\r\n          \"Stored\"\r\n        ],\r\n        \"enum\": [\r\n          \"swipe\",\r\n          \"dip\",\r\n          \"tap\",\r\n          \"keyed\",\r\n          \"stored\"\r\n        ]\r\n      },\r\n      \"Merchant\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"entityId\",\r\n          \"storeId\"\r\n        ],\r\n        \"properties\": {\r\n          \"entityId\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"The unique identifier for the parent entity of the retailer or the processing entity.\"\r\n          },\r\n          \"storeId\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"The unique identifier for the store.\"\r\n          },\r\n          \"terminalId\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"The identifier for the terminal.\",\r\n            \"nullable\": true\r\n          },\r\n          \"mcc\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"The merchant category code.\",\r\n            \"nullable\": true\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"BIResponse\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"response\",\r\n          \"nbInternalTrace\"\r\n        ],\r\n        \"properties\": {\r\n          \"response\": {\r\n            \"$ref\": \"#/components/schemas/Response6\"\r\n          },\r\n          \"nbInternalTrace\": {\r\n            \"$ref\": \"#/components/schemas/NationsInternalTrace\"\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"Response6\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"balances\": {\r\n            \"type\": \"array\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/BalanceItem\"\r\n            }\r\n          },\r\n          \"status\": {\r\n            \"description\": \"Additional status information in human readable form\",\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/Response6Status\"\r\n              }\r\n            ]\r\n          },\r\n          \"code\": {\r\n            \"description\": \"Additional status information\",\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/Response6Code\"\r\n              }\r\n            ]\r\n          },\r\n          \"applicationVersion\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"details\": {\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/ExceptionDetails\"\r\n              }\r\n            ]\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"BalanceItem\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"purseId\",\r\n          \"amount\"\r\n        ],\r\n        \"properties\": {\r\n          \"purseId\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"Internal purse identifier\"\r\n          },\r\n          \"purseName\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"The human readable name of the purse (e.g. \\\"OTC\\\", \\\"Healthy Living\\\")\",\r\n            \"nullable\": true\r\n          },\r\n          \"priority\": {\r\n            \"type\": \"number\",\r\n            \"description\": \"Relative priority for this purse, lower is higher priority\",\r\n            \"format\": \"double\",\r\n            \"nullable\": true\r\n          },\r\n          \"amount\": {\r\n            \"type\": \"number\",\r\n            \"description\": \"The current amount in the purse.\",\r\n            \"format\": \"double\"\r\n          },\r\n          \"currencyCode\": {\r\n            \"description\": \"The currency code for the transaction.\",\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/BalanceItemCurrencyCode\"\r\n              }\r\n            ]\r\n          },\r\n          \"expiration\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"The date when the balance will expire (e.g., \\\"2023-07-19\\\"). Note that these dates will be in ET.  This value may be omitted.\",\r\n            \"nullable\": true\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"BalanceItemCurrencyCode\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"USD\",\r\n          \"CAD\"\r\n        ],\r\n        \"enum\": [\r\n          \"USD\",\r\n          \"CAD\"\r\n        ]\r\n      },\r\n      \"Response6Status\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"Balance_inquiry_successful\",\r\n          \"Card_reported_lost_or_stolen\",\r\n          \"Member_not_found\",\r\n          \"Merchant_not_found\",\r\n          \"Insufficient_data_provided_to_complete_the_balance_inquiry_request\",\r\n          \"Card_not_activated\",\r\n          \"Fraud_suspected\",\r\n          \"No_active_purses_found_for_this_member\",\r\n          \"Card_not_enabled_for_balance_inquiry\",\r\n          \"General_exception\"\r\n        ],\r\n        \"enum\": [\r\n          \"Balance inquiry successful\",\r\n          \"Card reported lost or stolen\",\r\n          \"Member not found\",\r\n          \"Merchant not found\",\r\n          \"Insufficient data provided to complete the balance inquiry request\",\r\n          \"Card not activated\",\r\n          \"Fraud suspected\",\r\n          \"No active purses found for this member\",\r\n          \"Card not enabled for balance inquiry\",\r\n          \"General exception\"\r\n        ]\r\n      },\r\n      \"Response6Code\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"_000\",\r\n          \"_201\",\r\n          \"_202\",\r\n          \"_203\",\r\n          \"_204\",\r\n          \"_205\",\r\n          \"_206\",\r\n          \"_210\",\r\n          \"_211\",\r\n          \"_300\"\r\n        ],\r\n        \"enum\": [\r\n          \"000\",\r\n          \"201\",\r\n          \"202\",\r\n          \"203\",\r\n          \"204\",\r\n          \"205\",\r\n          \"206\",\r\n          \"210\",\r\n          \"211\",\r\n          \"300\"\r\n        ]\r\n      },\r\n      \"BIRequest\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"member\",\r\n          \"merchant\"\r\n        ],\r\n        \"properties\": {\r\n          \"member\": {\r\n            \"$ref\": \"#/components/schemas/Member\"\r\n          },\r\n          \"merchant\": {\r\n            \"$ref\": \"#/components/schemas/Merchant\"\r\n          },\r\n          \"applicationVersion\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"AnalyzeResponse\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"response\",\r\n          \"nbInternalTrace\"\r\n        ],\r\n        \"properties\": {\r\n          \"response\": {\r\n            \"$ref\": \"#/components/schemas/Authorization\"\r\n          },\r\n          \"nbInternalTrace\": {\r\n            \"$ref\": \"#/components/schemas/NationsInternalTrace\"\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"Authorization\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"merchantTransactionId\",\r\n          \"nationsBenefitsTransactionId\",\r\n          \"authorizedTransactionAmount\",\r\n          \"status\",\r\n          \"code\",\r\n          \"authorizationDetails\",\r\n          \"purseUtilization\"\r\n        ],\r\n        \"properties\": {\r\n          \"merchantTransactionId\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"Retailer generated unique identifier for the transaction.\"\r\n          },\r\n          \"nationsBenefitsTransactionId\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"The unique identifier for the transaction generated by Nations.\"\r\n          },\r\n          \"authorizedTransactionAmount\": {\r\n            \"type\": \"number\",\r\n            \"description\": \"The total authorized transaction amount (includes tax and refundable deposits).\",\r\n            \"format\": \"double\"\r\n          },\r\n          \"status\": {\r\n            \"description\": \"Authorization reason provides detailed information about the transaction status\",\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/AuthorizationStatus\"\r\n              }\r\n            ]\r\n          },\r\n          \"code\": {\r\n            \"description\": \"Authorization code represents the status of the transaction\",\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/AuthorizationCode\"\r\n              }\r\n            ]\r\n          },\r\n          \"authorizationDetails\": {\r\n            \"type\": \"array\",\r\n            \"description\": \"The details of authorization at a line item level.\",\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/AuthLineItemDetails\"\r\n            }\r\n          },\r\n          \"purseUtilization\": {\r\n            \"type\": \"array\",\r\n            \"description\": \"Array of purse utilization details.\",\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/PurseUsage\"\r\n            }\r\n          },\r\n          \"maxFundingAmount\": {\r\n            \"description\": \"Maximum amount that will be funded for this transaction to allow for retailer funding options.\",\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/AuthorizationMaxFundingAmount\"\r\n              }\r\n            ]\r\n          },\r\n          \"details\": {\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/ExceptionDetails\"\r\n              }\r\n            ]\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"AuthorizationStatus\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"Transaction_fully_approved__more_details_at_line_items\",\r\n          \"Transaction_partially_approved__more_details_at_line_items\",\r\n          \"Transaction_entirely_declined_due_to_unauthorized_products_in_the_basket\",\r\n          \"Transaction_entirely_declined_due_to_card_reported_lost_or_stolen\",\r\n          \"Transaction_entirely_declined_due_to_card_not_activated\",\r\n          \"Transaction_entirely_declined_due_to_no_funds_in_the_purses\",\r\n          \"Transaction_entirely_declined_due_to_other_membership_issues__contact_nations_for_more_details\",\r\n          \"Transaction_entirely_declined_due_purchase_at_an_unauthorized_store_location_or_online_retailer_or_country\",\r\n          \"Transaction_entirely_declined_due_suspected_fraud\",\r\n          \"Transaction_entirely_declined_because_mixed_transactions_are_not_supported_when_maxFundingAmount_is_used\",\r\n          \"Transaction_entirely_declined_because_prior_transaction_id_could_not_be_found\",\r\n          \"Transaction_could_not_be_processed_due_to_no_items_sent_in_the_transaction\",\r\n          \"Transaction_could_not_be_processed_because_dependent_transaction_could_not_be_found\",\r\n          \"Transaction_entirely_declined_due_to_card_not_being_enabled_for_this_transaction\"\r\n        ],\r\n        \"enum\": [\r\n          \"Transaction fully approved; more details at line items\",\r\n          \"Transaction partially approved; more details at line items\",\r\n          \"Transaction entirely declined due to unauthorized products in the basket\",\r\n          \"Transaction entirely declined due to card reported lost or stolen\",\r\n          \"Transaction entirely declined due to card not activated\",\r\n          \"Transaction entirely declined due to no funds in the purses\",\r\n          \"Transaction entirely declined due to other membership issues. contact nations for more details\",\r\n          \"Transaction entirely declined due purchase at an unauthorized store location or online retailer or country\",\r\n          \"Transaction entirely declined due suspected fraud\",\r\n          \"Transaction entirely declined because mixed transactions are not supported when maxFundingAmount is used\",\r\n          \"Transaction entirely declined because prior transaction id could not be found\",\r\n          \"Transaction could not be processed due to no items sent in the transaction\",\r\n          \"Transaction could not be processed because dependent transaction could not be found\",\r\n          \"Transaction entirely declined due to card not being enabled for this transaction\"\r\n        ]\r\n      },\r\n      \"AuthorizationCode\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"_000\",\r\n          \"_100\",\r\n          \"_200\",\r\n          \"_201\",\r\n          \"_202\",\r\n          \"_203\",\r\n          \"_204\",\r\n          \"_205\",\r\n          \"_206\",\r\n          \"_207\",\r\n          \"_208\",\r\n          \"_209\",\r\n          \"_210\",\r\n          \"_211\"\r\n        ],\r\n        \"enum\": [\r\n          \"000\",\r\n          \"100\",\r\n          \"200\",\r\n          \"201\",\r\n          \"202\",\r\n          \"203\",\r\n          \"204\",\r\n          \"205\",\r\n          \"206\",\r\n          \"207\",\r\n          \"208\",\r\n          \"209\",\r\n          \"210\",\r\n          \"211\"\r\n        ]\r\n      },\r\n      \"AuthLineItemDetails\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"lineItemNumber\",\r\n          \"productCode\",\r\n          \"productCodeType\",\r\n          \"authorizedAmountBeforeTax\",\r\n          \"authorizedTax\",\r\n          \"authorizedFees\",\r\n          \"authResult\",\r\n          \"benefitName\",\r\n          \"code\",\r\n          \"status\"\r\n        ],\r\n        \"properties\": {\r\n          \"lineItemNumber\": {\r\n            \"type\": \"integer\",\r\n            \"description\": \"The line item number.\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"productCode\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"The product code as passed in the request for the line item.\"\r\n          },\r\n          \"productCodeType\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"The product code type as passed in the request for the line item.\"\r\n          },\r\n          \"authorizedAmountBeforeTax\": {\r\n            \"type\": \"number\",\r\n            \"description\": \"The authorized transaction amount (does not include tax and refundable deposits) for this line item.\",\r\n            \"format\": \"double\"\r\n          },\r\n          \"authorizedTax\": {\r\n            \"type\": \"number\",\r\n            \"description\": \"The authorized tax for this line item.\",\r\n            \"format\": \"double\"\r\n          },\r\n          \"authorizedFees\": {\r\n            \"type\": \"number\",\r\n            \"description\": \"The authorized fee for this line item.\",\r\n            \"format\": \"double\"\r\n          },\r\n          \"authResult\": {\r\n            \"description\": \"Summary of the authorization.\",\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/AuthLineItemDetailsAuthResult\"\r\n              }\r\n            ]\r\n          },\r\n          \"benefitName\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"The name of the benefit type authorized for this line item. NA = No Benefit.\"\r\n          },\r\n          \"code\": {\r\n            \"type\": \"number\",\r\n            \"description\": \"Authorization code represents the status of the transaction\",\r\n            \"format\": \"double\"\r\n          },\r\n          \"status\": {\r\n            \"description\": \"Authorization reason provides detailed information about the transaction status\",\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/AuthLineItemDetailsStatus\"\r\n              }\r\n            ]\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"AuthLineItemDetailsAuthResult\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"APPROVED\",\r\n          \"DECLINED\",\r\n          \"APPROVED_PARTIAL\"\r\n        ],\r\n        \"enum\": [\r\n          \"APPROVED\",\r\n          \"DECLINED\",\r\n          \"APPROVED_PARTIAL\"\r\n        ]\r\n      },\r\n      \"AuthLineItemDetailsStatus\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"Approved__and_sufficient_funds_in_purse\",\r\n          \"Approved_product__purchase_exceeds_maxFundingAmount\",\r\n          \"Approved_product__insufficient_purse_funds\",\r\n          \"Exceeded_item_limit_for_specific_product__but_some_quantity_approved\",\r\n          \"Exceeded_dollar_limit_for_specific_product__but_partial_dollar_amount_approved\",\r\n          \"Approved_product_but_no_funds_in_purse\",\r\n          \"Attempted_to_purchase_a_nonAllowable_item__not_on_APL_\",\r\n          \"Exceeded_item_limit_for_specific_product_or_category\",\r\n          \"Exceeded_dollar_limit_for_specific_product_or_category\",\r\n          \"Unable_to_process_this_item_due_to_format_issue\",\r\n          \"Custom_name_value_pair_was_not_recognized_causing_item_to_not_be_processed\",\r\n          \"Return_approved__funds_will_be_restored_to_purse\",\r\n          \"Approved_product__return_exceeds_maxFundingAmount\",\r\n          \"Return_exceeds_original_purchase_quantity__but_partial_return_approved\",\r\n          \"Return_exceeds_original_purchase_amount__but_partial_return_approved\",\r\n          \"Return_attempted_for_nonAllowable_item__not_on_APL_\",\r\n          \"Return_would_exceed_purse_dollar_limit\",\r\n          \"No_record_of_such_an_item_being_purchased_by_the_member\",\r\n          \"Return_attempted_after_allowed_period__180_days_after_purchase_\"\r\n        ],\r\n        \"enum\": [\r\n          \"Approved, and sufficient funds in purse\",\r\n          \"Approved product, purchase exceeds maxFundingAmount\",\r\n          \"Approved product, insufficient purse funds\",\r\n          \"Exceeded item limit for specific product, but some quantity approved\",\r\n          \"Exceeded dollar limit for specific product, but partial dollar amount approved\",\r\n          \"Approved product but no funds in purse\",\r\n          \"Attempted to purchase a non-allowable item (not on APL)\",\r\n          \"Exceeded item limit for specific product or category\",\r\n          \"Exceeded dollar limit for specific product or category\",\r\n          \"Unable to process this item due to format issue\",\r\n          \"Custom name value pair was not recognized causing item to not be processed\",\r\n          \"Return approved, funds will be restored to purse\",\r\n          \"Approved product, return exceeds maxFundingAmount\",\r\n          \"Return exceeds original purchase quantity, but partial return approved\",\r\n          \"Return exceeds original purchase amount, but partial return approved\",\r\n          \"Return attempted for non-allowable item (not on APL)\",\r\n          \"Return would exceed purse dollar limit\",\r\n          \"No record of such an item being purchased by the member\",\r\n          \"Return attempted after allowed period (180 days after purchase)\"\r\n        ]\r\n      },\r\n      \"PurseUsage\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"purseName\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"Name of the purse.\",\r\n            \"nullable\": true\r\n          },\r\n          \"amountUsed\": {\r\n            \"type\": \"number\",\r\n            \"description\": \"Amount used from the purse.\",\r\n            \"format\": \"double\",\r\n            \"nullable\": true\r\n          },\r\n          \"remainingBalance\": {\r\n            \"type\": \"number\",\r\n            \"description\": \"Remaining balance in the purse.\",\r\n            \"format\": \"double\",\r\n            \"nullable\": true\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"AuthorizationMaxFundingAmount\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"USD\",\r\n          \"CAD\"\r\n        ],\r\n        \"enum\": [\r\n          \"USD\",\r\n          \"CAD\"\r\n        ]\r\n      },\r\n      \"AnalyzeRequest\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"member\",\r\n          \"merchant\",\r\n          \"transaction\"\r\n        ],\r\n        \"properties\": {\r\n          \"member\": {\r\n            \"$ref\": \"#/components/schemas/Member\"\r\n          },\r\n          \"merchant\": {\r\n            \"$ref\": \"#/components/schemas/Merchant\"\r\n          },\r\n          \"transaction\": {\r\n            \"$ref\": \"#/components/schemas/Transaction\"\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"Transaction\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"merchantTransactionId\",\r\n          \"transactionLocalDateTime\",\r\n          \"transactionCurrencyCode\",\r\n          \"lineItems\"\r\n        ],\r\n        \"properties\": {\r\n          \"merchantTransactionId\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"Merchant generated unique identifier for the transaction.\"\r\n          },\r\n          \"transactionLocalDateTime\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"ISO 8601; The local date and time when the transaction was made, represented in local time and expressed with its UTC offset (e.g., \\\"2023-07-19T13:20:30+01:00\\\").\"\r\n          },\r\n          \"transactionCurrencyCode\": {\r\n            \"description\": \"The currency code for the transaction.\",\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/TransactionCurrencyCode\"\r\n              }\r\n            ]\r\n          },\r\n          \"lineItems\": {\r\n            \"type\": \"array\",\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/LineItem\"\r\n            }\r\n          },\r\n          \"maxFundingAmount\": {\r\n            \"description\": \"Maximum amount that will be funded for this transaction to allow for retailer funding options.\",\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/TransactionMaxFundingAmount\"\r\n              }\r\n            ]\r\n          },\r\n          \"parentNationsBenefitsTransactionId\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"The NationsBenefits transaction ID that was used in a related transaction, e.g. eCommerce initial order capture.\",\r\n            \"nullable\": true\r\n          },\r\n          \"transactionRetailer\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"The name of the retailer at which the transaction was executed.\",\r\n            \"nullable\": true\r\n          },\r\n          \"transactionRetailerLocation\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"The the address of the retailer at which the trasnaction was executed.\",\r\n            \"nullable\": true\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"TransactionCurrencyCode\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"USD\",\r\n          \"CAD\"\r\n        ],\r\n        \"enum\": [\r\n          \"USD\",\r\n          \"CAD\"\r\n        ]\r\n      },\r\n      \"LineItem\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"lineItemNumber\",\r\n          \"productCodeType\",\r\n          \"productCode\",\r\n          \"unitPrice\",\r\n          \"quantity\",\r\n          \"units\"\r\n        ],\r\n        \"properties\": {\r\n          \"lineItemNumber\": {\r\n            \"type\": \"integer\",\r\n            \"description\": \"The line item number.\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"productCodeType\": {\r\n            \"description\": \"The type of the product code.\",\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/LineItemProductCodeType\"\r\n              }\r\n            ]\r\n          },\r\n          \"productCode\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"The unique product identifier. If the productCodeType = 'CAT_SUBCAT', the code would be a combination of item product category code and sub category code delimited by '|'\"\r\n          },\r\n          \"unitPrice\": {\r\n            \"type\": \"number\",\r\n            \"description\": \"The unit price of the product. This number is positive for sale and negative for a return.\",\r\n            \"format\": \"double\"\r\n          },\r\n          \"quantity\": {\r\n            \"type\": \"number\",\r\n            \"description\": \"The quantity of the product. If negative value is passed, it will be interpreted as positive.\",\r\n            \"format\": \"double\"\r\n          },\r\n          \"units\": {\r\n            \"description\": \"The unit of weight or volume for the product. Use 'COUNT' for countable quantities.\",\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/LineItemUnits\"\r\n              }\r\n            ]\r\n          },\r\n          \"taxes\": {\r\n            \"type\": \"array\",\r\n            \"description\": \"This array contains tax details for the line item.\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/TaxDetails\"\r\n            }\r\n          },\r\n          \"fees\": {\r\n            \"type\": \"array\",\r\n            \"description\": \"This object contains fees such as CRV and Bottle refund details for the line item.\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/FeesDetails\"\r\n            }\r\n          },\r\n          \"optionalFields\": {\r\n            \"type\": \"array\",\r\n            \"description\": \"This object contains key value pairs provided by retailer to perform custom actions in the request\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/OptionalFields\"\r\n            }\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"LineItemProductCodeType\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"UPC\",\r\n          \"PLU\",\r\n          \"GTIN\",\r\n          \"CAT_SUBCAT\",\r\n          \"ICD10\",\r\n          \"OTHER\"\r\n        ],\r\n        \"enum\": [\r\n          \"UPC\",\r\n          \"PLU\",\r\n          \"GTIN\",\r\n          \"CAT_SUBCAT\",\r\n          \"ICD10\",\r\n          \"OTHER\"\r\n        ]\r\n      },\r\n      \"LineItemUnits\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"COUNT\",\r\n          \"GM\",\r\n          \"OZ\",\r\n          \"LB\",\r\n          \"KG\",\r\n          \"GAL\",\r\n          \"FLOZ\",\r\n          \"L\",\r\n          \"ML\"\r\n        ],\r\n        \"enum\": [\r\n          \"COUNT\",\r\n          \"GM\",\r\n          \"OZ\",\r\n          \"LB\",\r\n          \"KG\",\r\n          \"GAL\",\r\n          \"FLOZ\",\r\n          \"L\",\r\n          \"ML\"\r\n        ]\r\n      },\r\n      \"TaxDetails\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"taxType\",\r\n          \"value\"\r\n        ],\r\n        \"properties\": {\r\n          \"taxType\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"Type of tax that is associated with the item.  Examples include SALES, VAT, GST, EXCISE, IMPORT_DUTY, LUXURY, OTHER\"\r\n          },\r\n          \"value\": {\r\n            \"type\": \"number\",\r\n            \"description\": \"Amount of the specific tax.\",\r\n            \"format\": \"double\"\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"FeesDetails\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"feeType\",\r\n          \"value\"\r\n        ],\r\n        \"properties\": {\r\n          \"feeType\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"Type of refundable deposit or other fees associated with an item.  Examples include DEPOSIT, TARE, COST_PLUS, OTHER\"\r\n          },\r\n          \"value\": {\r\n            \"type\": \"number\",\r\n            \"description\": \"Amount of the specific fees.\",\r\n            \"format\": \"double\"\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"OptionalFields\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"name\",\r\n          \"value\"\r\n        ],\r\n        \"properties\": {\r\n          \"name\": {\r\n            \"description\": \"Retailer specific option to provide to BAS generally for custom requests\"\r\n          },\r\n          \"value\": {\r\n            \"description\": \"Value associated with retailer specific option\"\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"TransactionMaxFundingAmount\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"USD\",\r\n          \"CAD\"\r\n        ],\r\n        \"enum\": [\r\n          \"USD\",\r\n          \"CAD\"\r\n        ]\r\n      },\r\n      \"RedeemResponse\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"response\",\r\n          \"nbInternalTrace\"\r\n        ],\r\n        \"properties\": {\r\n          \"response\": {\r\n            \"$ref\": \"#/components/schemas/Response7\"\r\n          },\r\n          \"nbInternalTrace\": {\r\n            \"$ref\": \"#/components/schemas/NationsInternalTrace\"\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"Response7\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"nationsBenefitsTransactionId\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"The unique identifier for the transaction that was generated by NationsBenefits in response to the Analyze call.\",\r\n            \"nullable\": true\r\n          },\r\n          \"status\": {\r\n            \"description\": \"The status of the redemption.\",\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/Response7Status\"\r\n              }\r\n            ]\r\n          },\r\n          \"code\": {\r\n            \"description\": \"The code associated with the redemption status.\",\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/Response7Code\"\r\n              }\r\n            ]\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"Response7Status\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"REDEMPTION_SUCCESSFUL\",\r\n          \"REDEMPTION_FAILED_INCORRECT_AMOUNT_REDEEMED\",\r\n          \"REDEMPTION_FAILED_DUPLICATE_REDEMPTION_REQUEST\",\r\n          \"REDEMPTION_FAILED_INVALID_NB_TRANSACTION_ID\",\r\n          \"REDEMPTION_FAILED_OTHER\"\r\n        ],\r\n        \"enum\": [\r\n          \"REDEMPTION_SUCCESSFUL\",\r\n          \"REDEMPTION_FAILED_INCORRECT_AMOUNT_REDEEMED\",\r\n          \"REDEMPTION_FAILED_DUPLICATE_REDEMPTION_REQUEST\",\r\n          \"REDEMPTION_FAILED_INVALID_NB_TRANSACTION_ID\",\r\n          \"REDEMPTION_FAILED_OTHER\"\r\n        ]\r\n      },\r\n      \"Response7Code\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"_000\",\r\n          \"_201\",\r\n          \"_202\",\r\n          \"_203\",\r\n          \"_204\"\r\n        ],\r\n        \"enum\": [\r\n          \"000\",\r\n          \"201\",\r\n          \"202\",\r\n          \"203\",\r\n          \"204\"\r\n        ]\r\n      },\r\n      \"RedeemRequest\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"nationsBenefitsTransactionId\",\r\n          \"redeemedAmount\"\r\n        ],\r\n        \"properties\": {\r\n          \"nationsBenefitsTransactionId\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"The unique identifier for the transaction that was generated by NationsBenefits in response to the Analyze call.\"\r\n          },\r\n          \"redeemType\": {\r\n            \"description\": \"Type of the redemption transaction. Each type corresponds to different transaction actions internally: - \\\"000\\\": Default type if not provided, often representing generic handshake representing authorized transaction amount redemption from the Issuer (happy-path). - \\\"100\\\": For voiding a previously authorized request/transaction. - \\\"200\\\": For reversing a previously authorized request/transaction. - \\\"300\\\": For voiding an auth, used in the scenarios where the POS/merchant receives an incorrect amount ($) from the Issuer. Defaults to '000' if not provided.\",\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/RedeemRequestRedeemType\"\r\n              }\r\n            ]\r\n          },\r\n          \"redeemedAmount\": {\r\n            \"type\": \"number\",\r\n            \"description\": \"The redeemed amount.\",\r\n            \"format\": \"double\"\r\n          },\r\n          \"merchantDiscretionaryData\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"Optional data field for the merchant to send additional information.\",\r\n            \"nullable\": true\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"RedeemRequestRedeemType\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"_000\",\r\n          \"_100\",\r\n          \"_200\",\r\n          \"_300\"\r\n        ],\r\n        \"enum\": [\r\n          \"000\",\r\n          \"100\",\r\n          \"200\",\r\n          \"300\"\r\n        ]\r\n      },\r\n      \"VoidResponse\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"response\",\r\n          \"nbInternalTrace\"\r\n        ],\r\n        \"properties\": {\r\n          \"response\": {\r\n            \"$ref\": \"#/components/schemas/Response8\"\r\n          },\r\n          \"merchantDiscretionaryData\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"Optional data field for the merchant to send additional information.\",\r\n            \"nullable\": true\r\n          },\r\n          \"nbInternalTrace\": {\r\n            \"$ref\": \"#/components/schemas/NationsInternalTrace\"\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"Response8\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"nationsBenefitsTransactionId\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"The unique identifier for the transaction that was generated by NationsBenefits in response to the Analyze call.\",\r\n            \"nullable\": true\r\n          },\r\n          \"status\": {\r\n            \"description\": \"The status of the void.\",\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/Response8Status\"\r\n              }\r\n            ]\r\n          },\r\n          \"code\": {\r\n            \"description\": \"The code associated with the void status.\",\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/Response8Code\"\r\n              }\r\n            ]\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"Response8Status\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"VOID_SUCCESSFUL\",\r\n          \"VOID_FAILED_UNRECOGNIZED_NB_ID\",\r\n          \"VOID_FAILED_DUPLICATE_REQUEST\",\r\n          \"VOID_FAILED_OTHER\"\r\n        ],\r\n        \"enum\": [\r\n          \"VOID_SUCCESSFUL\",\r\n          \"VOID_FAILED_UNRECOGNIZED_NB_ID\",\r\n          \"VOID_FAILED_DUPLICATE_REQUEST\",\r\n          \"VOID_FAILED_OTHER\"\r\n        ]\r\n      },\r\n      \"Response8Code\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"_000\",\r\n          \"_201\",\r\n          \"_202\",\r\n          \"_203\"\r\n        ],\r\n        \"enum\": [\r\n          \"000\",\r\n          \"201\",\r\n          \"202\",\r\n          \"203\"\r\n        ]\r\n      },\r\n      \"VoidRequest\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"nationsBenefitsTransactionId\"\r\n        ],\r\n        \"properties\": {\r\n          \"nationsBenefitsTransactionId\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"The unique identifier for the transaction that was generated by BAS in response to the Analyze call.\"\r\n          },\r\n          \"merchantDiscretionaryData\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"Optional data field for the merchant to send additional information.\",\r\n            \"nullable\": true\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"ReversalResponse\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"response\",\r\n          \"nbInternalTrace\"\r\n        ],\r\n        \"properties\": {\r\n          \"response\": {\r\n            \"$ref\": \"#/components/schemas/Response9\"\r\n          },\r\n          \"merchantDiscretionaryData\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"Optional data field for the merchant to send additional information.\",\r\n            \"nullable\": true\r\n          },\r\n          \"nbInternalTrace\": {\r\n            \"$ref\": \"#/components/schemas/NationsInternalTrace\"\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"Response9\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"nationsBenefitsTransactionId\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"The unique identifier for the transaction that was generated by NationsBenefits in response to the Analyze call.\",\r\n            \"nullable\": true\r\n          },\r\n          \"status\": {\r\n            \"description\": \"The status of the reversal.\",\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/Response9Status\"\r\n              }\r\n            ]\r\n          },\r\n          \"code\": {\r\n            \"description\": \"The code associated with the reversal status.\",\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/Response9Code\"\r\n              }\r\n            ]\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"Response9Status\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"REVERSAL_SUCCESSFUL\",\r\n          \"REVERSAL_FAILED_UNRECOGNIZED_NB_ID\",\r\n          \"REVERSAL_FAILED_DUPLICATE_REQUEST\",\r\n          \"REVERSAL_FAILED_OTHER\"\r\n        ],\r\n        \"enum\": [\r\n          \"REVERSAL_SUCCESSFUL\",\r\n          \"REVERSAL_FAILED_UNRECOGNIZED_NB_ID\",\r\n          \"REVERSAL_FAILED_DUPLICATE_REQUEST\",\r\n          \"REVERSAL_FAILED_OTHER\"\r\n        ]\r\n      },\r\n      \"Response9Code\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"_000\",\r\n          \"_201\",\r\n          \"_202\",\r\n          \"_203\"\r\n        ],\r\n        \"enum\": [\r\n          \"000\",\r\n          \"201\",\r\n          \"202\",\r\n          \"203\"\r\n        ]\r\n      },\r\n      \"ReversalRequest\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"nationsBenefitsTransactionId\"\r\n        ],\r\n        \"properties\": {\r\n          \"nationsBenefitsTransactionId\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"The unique identifier for the transaction that was generated by BAS in response to the Analyze call.  Note that this value will not be required if a combination of the entityId, merchantTransactionId, and transactionLocalDateTime are all provided.\"\r\n          },\r\n          \"merchantDiscretionaryData\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"Optional data field for the merchant to send additional information.\",\r\n            \"nullable\": true\r\n          },\r\n          \"entityId\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"The unique identifier for the parent entity of the retailer or the processing entity.\",\r\n            \"nullable\": true\r\n          },\r\n          \"storeId\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"The unique identifier for the store of the retailer or the processing entity.\",\r\n            \"nullable\": true\r\n          },\r\n          \"terminalId\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"The unique identifier for the termina entity of the retailer or the processing entity.\",\r\n            \"nullable\": true\r\n          },\r\n          \"merchantTransactionId\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"Merchant generated unique identifier for the transaction.\",\r\n            \"nullable\": true\r\n          },\r\n          \"transactionLocalDateTime\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"ISO 8601; The local date and time when the transaction was made, represented in local time and expressed with its UTC offset (e.g., \\\"2023-07-19T13:20:30+01:00\\\").\",\r\n            \"nullable\": true\r\n          },\r\n          \"additionalProperties\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {}\r\n          }\r\n        }\r\n      },\r\n      \"NegativePriceProductMovementDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"upc\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"datetime\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\",\r\n            \"nullable\": true\r\n          },\r\n          \"description\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"transactionNumber\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"qty\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\"\r\n          },\r\n          \"sales\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\"\r\n          },\r\n          \"storeId\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"storeName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"TransactionResponse\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"transation\": {\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/TransationTan\"\r\n              }\r\n            ]\r\n          },\r\n          \"amount\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"transationStatue\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"TransationTan\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"total\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"double\"\r\n          },\r\n          \"tax\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"double\"\r\n          },\r\n          \"subtotal\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"double\"\r\n          },\r\n          \"cardtype\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"items\": {\r\n            \"type\": \"array\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/TransationItem\"\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"TransationItem\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"description\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"qty\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"double\"\r\n          },\r\n          \"price\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"double\"\r\n          }\r\n        }\r\n      },\r\n      \"ValidationProblemDetails\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": {\r\n          \"nullable\": true\r\n        },\r\n        \"properties\": {\r\n          \"type\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"title\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"status\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\",\r\n            \"nullable\": true\r\n          },\r\n          \"detail\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"instance\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"errors\": {\r\n            \"type\": \"object\",\r\n            \"nullable\": true,\r\n            \"additionalProperties\": {\r\n              \"type\": \"array\",\r\n              \"items\": {\r\n                \"type\": \"string\"\r\n              }\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"UserPayment\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"cardNumber\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"pin\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"cardName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"cardDate\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\"\r\n          },\r\n          \"cvc\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"zip\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"CustomerResponse\": {\r\n        \"type\": \"object\",\r\n        \"description\": \"登录后返回数据\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"user\"\r\n        ],\r\n        \"properties\": {\r\n          \"user\": {\r\n            \"$ref\": \"#/components/schemas/UserItem\"\r\n          },\r\n          \"token\": {\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/RegToken\"\r\n              }\r\n            ]\r\n          }\r\n        }\r\n      },\r\n      \"UserItem\": {\r\n        \"type\": \"object\",\r\n        \"description\": \"登录后用户数据\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"id\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"firstName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"lastName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"address\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"city\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"birthday\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\",\r\n            \"nullable\": true\r\n          },\r\n          \"email\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"zipCode\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"storeName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"cashRegisterID\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"phonenumber\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"defaultTime\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"pointBalance\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\",\r\n            \"nullable\": true\r\n          },\r\n          \"greetingMessage\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"logoLink\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"primaryColor\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"RegToken\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"registerToken\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"RegisterRequest\": {\r\n        \"type\": \"object\",\r\n        \"description\": \"注册后用户数据类\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"firstName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"lastName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"address\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"city\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"birthday\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"email\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"phonenumber\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"defaultTime\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"zipCode\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"CustomUpdateRequest\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"firstName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"lastName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"address\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"city\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"birthday\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"email\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"defaultTime\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"zipCode\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"CompanyProductsDTO\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"products\": {\r\n            \"type\": \"array\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/StoreProductsDTO\"\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"StoreProductsDTO\": {\r\n        \"type\": \"object\",\r\n        \"description\": \"商店下的商品\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"upc\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"isScale\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"everyDayLowPrice\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"productName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"vendorID\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"department\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"itemSize\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\",\r\n            \"nullable\": true\r\n          },\r\n          \"itemUnit\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"priceBase\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"unitCost\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\",\r\n            \"nullable\": true\r\n          },\r\n          \"regular_Price\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\",\r\n            \"nullable\": true\r\n          },\r\n          \"pluUseSingleSplitPrice\": {\r\n            \"type\": \"boolean\",\r\n            \"nullable\": true\r\n          },\r\n          \"pluSingleSplitPrice\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\",\r\n            \"nullable\": true\r\n          },\r\n          \"beginDate\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\",\r\n            \"nullable\": true\r\n          },\r\n          \"endDate\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\",\r\n            \"nullable\": true\r\n          },\r\n          \"promoDescription\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"plu_id\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\",\r\n            \"nullable\": true\r\n          },\r\n          \"last_Sold\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\",\r\n            \"nullable\": true\r\n          },\r\n          \"last_Received\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\",\r\n            \"nullable\": true\r\n          },\r\n          \"onHand\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\",\r\n            \"nullable\": true\r\n          },\r\n          \"deposit\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\",\r\n            \"nullable\": true\r\n          },\r\n          \"ptD30\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"double\",\r\n            \"nullable\": true\r\n          },\r\n          \"aisle\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"store\": {\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/CompanyStoreDTO\"\r\n              }\r\n            ]\r\n          }\r\n        }\r\n      },\r\n      \"CompanyStoreDTO\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"storeName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"storeId\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          }\r\n        }\r\n      },\r\n      \"ProblemDetails\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": {\r\n          \"nullable\": true\r\n        },\r\n        \"properties\": {\r\n          \"type\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"title\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"status\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\",\r\n            \"nullable\": true\r\n          },\r\n          \"detail\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"instance\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"ProductImageSynchronizationBody\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"sourceStoreAccessKeys\": {\r\n            \"type\": \"array\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"type\": \"string\"\r\n            }\r\n          },\r\n          \"pluUpcs\": {\r\n            \"type\": \"array\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"type\": \"string\"\r\n            }\r\n          },\r\n          \"mode\": {\r\n            \"$ref\": \"#/components/schemas/ProductImageSynchronizationMode\"\r\n          }\r\n        }\r\n      },\r\n      \"ProductImageSynchronizationMode\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"SkipIfExists\",\r\n          \"Cover\"\r\n        ],\r\n        \"enum\": [\r\n          \"SkipIfExists\",\r\n          \"Cover\"\r\n        ]\r\n      },\r\n      \"BarcodeInfo\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"barcode\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"deposit\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"department\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"BarcodesBody\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"barcodes\": {\r\n            \"type\": \"array\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"type\": \"string\"\r\n            }\r\n          },\r\n          \"storeIds\": {\r\n            \"type\": \"array\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"ServiceResponseOfProductResult\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"statues\": {\r\n            \"$ref\": \"#/components/schemas/ServiceStatues\"\r\n          },\r\n          \"errorMessage\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"responseCode\": {\r\n            \"$ref\": \"#/components/schemas/HttpStatusCode\"\r\n          },\r\n          \"result\": {\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/ProductResult\"\r\n              }\r\n            ]\r\n          }\r\n        }\r\n      },\r\n      \"ServiceStatues\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"Success\",\r\n          \"Failure\"\r\n        ],\r\n        \"enum\": [\r\n          \"Success\",\r\n          \"Failure\"\r\n        ]\r\n      },\r\n      \"HttpStatusCode\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"Continue\",\r\n          \"SwitchingProtocols\",\r\n          \"Processing\",\r\n          \"EarlyHints\",\r\n          \"OK\",\r\n          \"Created\",\r\n          \"Accepted\",\r\n          \"NonAuthoritativeInformation\",\r\n          \"NoContent\",\r\n          \"ResetContent\",\r\n          \"PartialContent\",\r\n          \"MultiStatus\",\r\n          \"AlreadyReported\",\r\n          \"IMUsed\",\r\n          \"MultipleChoices\",\r\n          \"Ambiguous\",\r\n          \"MovedPermanently\",\r\n          \"Moved\",\r\n          \"Found\",\r\n          \"Redirect\",\r\n          \"SeeOther\",\r\n          \"RedirectMethod\",\r\n          \"NotModified\",\r\n          \"UseProxy\",\r\n          \"Unused\",\r\n          \"TemporaryRedirect\",\r\n          \"RedirectKeepVerb\",\r\n          \"PermanentRedirect\",\r\n          \"BadRequest\",\r\n          \"Unauthorized\",\r\n          \"PaymentRequired\",\r\n          \"Forbidden\",\r\n          \"NotFound\",\r\n          \"MethodNotAllowed\",\r\n          \"NotAcceptable\",\r\n          \"ProxyAuthenticationRequired\",\r\n          \"RequestTimeout\",\r\n          \"Conflict\",\r\n          \"Gone\",\r\n          \"LengthRequired\",\r\n          \"PreconditionFailed\",\r\n          \"RequestEntityTooLarge\",\r\n          \"RequestUriTooLong\",\r\n          \"UnsupportedMediaType\",\r\n          \"RequestedRangeNotSatisfiable\",\r\n          \"ExpectationFailed\",\r\n          \"MisdirectedRequest\",\r\n          \"UnprocessableEntity\",\r\n          \"Locked\",\r\n          \"FailedDependency\",\r\n          \"UpgradeRequired\",\r\n          \"PreconditionRequired\",\r\n          \"TooManyRequests\",\r\n          \"RequestHeaderFieldsTooLarge\",\r\n          \"UnavailableForLegalReasons\",\r\n          \"InternalServerError\",\r\n          \"NotImplemented\",\r\n          \"BadGateway\",\r\n          \"ServiceUnavailable\",\r\n          \"GatewayTimeout\",\r\n          \"HttpVersionNotSupported\",\r\n          \"VariantAlsoNegotiates\",\r\n          \"InsufficientStorage\",\r\n          \"LoopDetected\",\r\n          \"NotExtended\",\r\n          \"NetworkAuthenticationRequired\"\r\n        ],\r\n        \"enum\": [\r\n          \"Continue\",\r\n          \"SwitchingProtocols\",\r\n          \"Processing\",\r\n          \"EarlyHints\",\r\n          \"OK\",\r\n          \"Created\",\r\n          \"Accepted\",\r\n          \"NonAuthoritativeInformation\",\r\n          \"NoContent\",\r\n          \"ResetContent\",\r\n          \"PartialContent\",\r\n          \"MultiStatus\",\r\n          \"AlreadyReported\",\r\n          \"IMUsed\",\r\n          \"Ambiguous\",\r\n          \"Ambiguous\",\r\n          \"Moved\",\r\n          \"Moved\",\r\n          \"Redirect\",\r\n          \"Redirect\",\r\n          \"RedirectMethod\",\r\n          \"RedirectMethod\",\r\n          \"NotModified\",\r\n          \"UseProxy\",\r\n          \"Unused\",\r\n          \"TemporaryRedirect\",\r\n          \"TemporaryRedirect\",\r\n          \"PermanentRedirect\",\r\n          \"BadRequest\",\r\n          \"Unauthorized\",\r\n          \"PaymentRequired\",\r\n          \"Forbidden\",\r\n          \"NotFound\",\r\n          \"MethodNotAllowed\",\r\n          \"NotAcceptable\",\r\n          \"ProxyAuthenticationRequired\",\r\n          \"RequestTimeout\",\r\n          \"Conflict\",\r\n          \"Gone\",\r\n          \"LengthRequired\",\r\n          \"PreconditionFailed\",\r\n          \"RequestEntityTooLarge\",\r\n          \"RequestUriTooLong\",\r\n          \"UnsupportedMediaType\",\r\n          \"RequestedRangeNotSatisfiable\",\r\n          \"ExpectationFailed\",\r\n          \"MisdirectedRequest\",\r\n          \"UnprocessableEntity\",\r\n          \"Locked\",\r\n          \"FailedDependency\",\r\n          \"UpgradeRequired\",\r\n          \"PreconditionRequired\",\r\n          \"TooManyRequests\",\r\n          \"RequestHeaderFieldsTooLarge\",\r\n          \"UnavailableForLegalReasons\",\r\n          \"InternalServerError\",\r\n          \"NotImplemented\",\r\n          \"BadGateway\",\r\n          \"ServiceUnavailable\",\r\n          \"GatewayTimeout\",\r\n          \"HttpVersionNotSupported\",\r\n          \"VariantAlsoNegotiates\",\r\n          \"InsufficientStorage\",\r\n          \"LoopDetected\",\r\n          \"NotExtended\",\r\n          \"NetworkAuthenticationRequired\"\r\n        ]\r\n      },\r\n      \"ProductResult\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"items\": {\r\n            \"type\": \"array\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/WalmartProduct\"\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"WalmartProduct\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"name\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"categoryPath\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"shortDescription\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"longDescription\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"brandName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"largeImage\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"uri\",\r\n            \"nullable\": true\r\n          },\r\n          \"size\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"imageEntities\": {\r\n            \"type\": \"array\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/ImageEntity\"\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"ImageEntity\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"largeImage\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"uri\",\r\n            \"nullable\": true\r\n          },\r\n          \"entityType\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"KeyValuePairedOfIntegerAndString\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"key\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"value\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"ProductInfoDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"storeID\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"storeName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"productId\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\"\r\n          },\r\n          \"productDescript\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"barcode\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"department\": {\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/Department\"\r\n              }\r\n            ]\r\n          },\r\n          \"vendor\": {\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/Vendor\"\r\n              }\r\n            ]\r\n          },\r\n          \"brand\": {\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/Brand\"\r\n              }\r\n            ]\r\n          },\r\n          \"taxRuleSource\": {\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/TaxRuleSource\"\r\n              }\r\n            ]\r\n          },\r\n          \"taxs\": {\r\n            \"type\": \"array\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/TaxDto\"\r\n            }\r\n          },\r\n          \"price\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\"\r\n          },\r\n          \"priceBase\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"scaleSaleMode\": {\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/ScaleSaleMode\"\r\n              }\r\n            ]\r\n          },\r\n          \"departmentGroup\": {\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/DepartmentGroupDto\"\r\n              }\r\n            ]\r\n          }\r\n        }\r\n      },\r\n      \"TaxRuleSource\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"id\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"type\": {\r\n            \"$ref\": \"#/components/schemas/TaxRuleSourceType\"\r\n          },\r\n          \"description\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"TaxRuleSourceType\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"Department\",\r\n          \"Custom\"\r\n        ],\r\n        \"enum\": [\r\n          \"Department\",\r\n          \"Custom\"\r\n        ]\r\n      },\r\n      \"ScaleSaleMode\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"ByWeight\",\r\n          \"ByPC\"\r\n        ],\r\n        \"enum\": [\r\n          \"ByWeight\",\r\n          \"ByPC\"\r\n        ]\r\n      },\r\n      \"UpdateProductDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"storeId\",\r\n          \"productId\"\r\n        ],\r\n        \"properties\": {\r\n          \"storeId\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"productId\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\",\r\n            \"minLength\": 1\r\n          },\r\n          \"productDesccript\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"departmentId\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\",\r\n            \"nullable\": true\r\n          },\r\n          \"vendorDesc\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"brandId\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\",\r\n            \"nullable\": true\r\n          },\r\n          \"taxType\": {\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/TaxRuleSourceType\"\r\n              }\r\n            ]\r\n          },\r\n          \"taxIds\": {\r\n            \"type\": \"array\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"ProductReportResponse\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"products\": {\r\n            \"type\": \"array\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/ProductReport\"\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"ProductReport\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"barCode\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"description\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"priceBase\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"price\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\"\r\n          },\r\n          \"onhand\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"double\",\r\n            \"nullable\": true\r\n          },\r\n          \"lastSold\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\",\r\n            \"nullable\": true\r\n          },\r\n          \"createdDate\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"GetTokenRequestDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"userName\",\r\n          \"password\"\r\n        ],\r\n        \"properties\": {\r\n          \"userName\": {\r\n            \"type\": \"string\",\r\n            \"minLength\": 1\r\n          },\r\n          \"password\": {\r\n            \"type\": \"string\",\r\n            \"minLength\": 1\r\n          }\r\n        }\r\n      },\r\n      \"DiscountStoreCouponResponseDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"discountStoreCoupons\": {\r\n            \"type\": \"array\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/DiscountStoreCouponDto\"\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"DiscountStoreCouponDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"storeName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"code\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"description\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"qty\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"total\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"StoreListDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"stores\": {\r\n            \"type\": \"array\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/ReportStoreDto\"\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"ReportStoreDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"storeId\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"storeName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"startingHourOfTheDay\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"beginDayOfWeek\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          }\r\n        }\r\n      },\r\n      \"HourlySalesResponseDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"sales\": {\r\n            \"type\": \"array\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/HourlySalesDto\"\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"HourlySalesDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"hour\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"qty\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\",\r\n            \"nullable\": true\r\n          },\r\n          \"sales\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\",\r\n            \"nullable\": true\r\n          },\r\n          \"percentage\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\",\r\n            \"nullable\": true\r\n          },\r\n          \"trxCount\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"DepartmentGroupSalesResponseDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"departmentGroupSalesList\": {\r\n            \"type\": \"array\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/DepartmentGroupSalesDto\"\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"DepartmentGroupSalesDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"department\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"group\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"qty\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\",\r\n            \"nullable\": true\r\n          },\r\n          \"sales\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\",\r\n            \"nullable\": true\r\n          },\r\n          \"trxCount\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\",\r\n            \"nullable\": true\r\n          },\r\n          \"avgItemsTrx\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"TenderResponse\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"tenders\": {\r\n            \"type\": \"array\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/Tender\"\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"Tender\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"description\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"amount\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"double\"\r\n          },\r\n          \"count\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          }\r\n        }\r\n      },\r\n      \"SummaryResponseDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"regularRevenue\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\",\r\n            \"nullable\": true\r\n          },\r\n          \"returnQty\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\",\r\n            \"nullable\": true\r\n          },\r\n          \"return\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\",\r\n            \"nullable\": true\r\n          },\r\n          \"waste\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\",\r\n            \"nullable\": true\r\n          },\r\n          \"tax\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\",\r\n            \"nullable\": true\r\n          },\r\n          \"otherRevenue\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\",\r\n            \"nullable\": true\r\n          },\r\n          \"custCount\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\",\r\n            \"nullable\": true\r\n          },\r\n          \"loyaltyCustomer\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\",\r\n            \"nullable\": true\r\n          },\r\n          \"currentTime\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\",\r\n            \"nullable\": true\r\n          },\r\n          \"beginDatetime\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"endDatetime\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"HelloReply2Dto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"message\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"SettingConfigResponse\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"items\": {\r\n            \"type\": \"array\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/SettingConfigItem\"\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"SettingConfigItem\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"filedName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"visibilityStatus\": {\r\n            \"$ref\": \"#/components/schemas/FieldVisibility\"\r\n          },\r\n          \"selectStatus\": {\r\n            \"$ref\": \"#/components/schemas/FieldSelection\"\r\n          }\r\n        }\r\n      },\r\n      \"FieldVisibility\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"Show\",\r\n          \"Hide\"\r\n        ],\r\n        \"enum\": [\r\n          \"Show\",\r\n          \"Hide\"\r\n        ]\r\n      },\r\n      \"FieldSelection\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"Required\",\r\n          \"Optional\"\r\n        ],\r\n        \"enum\": [\r\n          \"Required\",\r\n          \"Optional\"\r\n        ]\r\n      },\r\n      \"SharedTemplate\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"templates\": {\r\n            \"type\": \"array\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/Template\"\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"Template\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"id\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\",\r\n            \"nullable\": true\r\n          },\r\n          \"templateName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"storeId\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          }\r\n        }\r\n      },\r\n      \"SearchTemplate\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"id\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\",\r\n            \"nullable\": true\r\n          },\r\n          \"templateName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"templateJson\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"lastModified\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\",\r\n            \"nullable\": true\r\n          },\r\n          \"created\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"StoreChainCodeType\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"Internal\",\r\n          \"Normal\",\r\n          \"None\"\r\n        ],\r\n        \"enum\": [\r\n          \"Internal\",\r\n          \"Normal\",\r\n          \"None\"\r\n        ]\r\n      },\r\n      \"StoreChainCodeInfo\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"id\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"colorId\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"desc\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"barcode\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"departmentDesc\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"vendorDesc\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"brandDesc\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"price\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\"\r\n          },\r\n          \"priceBase\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"scaleSaleMode\": {\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/ScaleSaleMode\"\r\n              }\r\n            ]\r\n          },\r\n          \"chainCode\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"chainDepartment\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"chainDescription\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"storeID\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"storeName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"UpdateProductChainCodeDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"storeCode\": {\r\n            \"type\": \"array\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/KeyValuePairedOfIntegerAndString\"\r\n            }\r\n          },\r\n          \"chainCodeInfo\": {\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/StoreChainCodeUpdateRequest\"\r\n              }\r\n            ]\r\n          }\r\n        }\r\n      },\r\n      \"StoreChainCodeUpdateRequest\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"chainCode\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"chainDepartment\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"chainDescription\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"KeyValuePairedOfIntegerAndBoolean\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"key\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"value\": {\r\n            \"type\": \"boolean\"\r\n          }\r\n        }\r\n      },\r\n      \"StoreDataCloneBody\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"currentStore\",\r\n          \"targentStore\",\r\n          \"barCodes\"\r\n        ],\r\n        \"properties\": {\r\n          \"currentStore\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"targentStore\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"barCodes\": {\r\n            \"type\": \"array\",\r\n            \"maxItems\": 500,\r\n            \"items\": {\r\n              \"type\": \"string\"\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"MutiBarcodeCloneBody\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"fromStore\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"toStore\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"mainItemBarcodes\": {\r\n            \"type\": \"array\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"type\": \"string\"\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"SearchType\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"Text\",\r\n          \"UPC\"\r\n        ],\r\n        \"enum\": [\r\n          \"Text\",\r\n          \"UPC\"\r\n        ]\r\n      },\r\n      \"StoreInventoryDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"storeId\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"storeName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"wareHouseID\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\",\r\n            \"nullable\": true\r\n          },\r\n          \"wareHouseName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"upc\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"商品条码\",\r\n            \"nullable\": true\r\n          },\r\n          \"productDescription\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"商品名\",\r\n            \"nullable\": true\r\n          },\r\n          \"brandDesc\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"vendorDesc\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"departmentDesc\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"onHand\": {\r\n            \"type\": \"string\",\r\n            \"description\": \"库存\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"SearchStoreInventroyRequest\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"storeIdList\",\r\n          \"barCodeList\"\r\n        ],\r\n        \"properties\": {\r\n          \"storeIdList\": {\r\n            \"type\": \"array\",\r\n            \"items\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            }\r\n          },\r\n          \"barCodeList\": {\r\n            \"type\": \"array\",\r\n            \"items\": {\r\n              \"type\": \"string\"\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"GetTransferResponseDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"description\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"id\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\"\r\n          },\r\n          \"currentStore\": {\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/StoreInfoDto\"\r\n              }\r\n            ]\r\n          },\r\n          \"targetStore\": {\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/StoreInfoDto\"\r\n              }\r\n            ]\r\n          },\r\n          \"currentWareHouse\": {\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/WareHouseInfoDto\"\r\n              }\r\n            ]\r\n          },\r\n          \"targetWareHouse\": {\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/WareHouseInfoDto\"\r\n              }\r\n            ]\r\n          },\r\n          \"createdDate\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\",\r\n            \"nullable\": true\r\n          },\r\n          \"transferDate\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\",\r\n            \"nullable\": true\r\n          },\r\n          \"receivedDate\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\",\r\n            \"nullable\": true\r\n          },\r\n          \"transferStatus\": {\r\n            \"$ref\": \"#/components/schemas/TransferStatus\"\r\n          },\r\n          \"totalQty\": {\r\n            \"type\": \"number\",\r\n            \"description\": \"商品总数量\",\r\n            \"format\": \"decimal\"\r\n          },\r\n          \"itemCount\": {\r\n            \"type\": \"integer\",\r\n            \"description\": \"商品条目数量\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"transferProductInfos\": {\r\n            \"type\": \"array\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/TransferProductInfo\"\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"StoreInfoDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"id\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"storeName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"WareHouseInfoDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"wareHouseId\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\"\r\n          },\r\n          \"wareHouseDesc\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"TransferStatus\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"Dispatched\",\r\n          \"Received\",\r\n          \"JustCreated\"\r\n        ],\r\n        \"enum\": [\r\n          \"Dispatched\",\r\n          \"Received\",\r\n          \"JustCreated\"\r\n        ]\r\n      },\r\n      \"TransferProductInfo\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"barCode\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"desciption\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"transferQty\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\"\r\n          },\r\n          \"receiveQty\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\",\r\n            \"nullable\": true\r\n          },\r\n          \"currentPrice\": {\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/TransferProductPrcie\"\r\n              }\r\n            ]\r\n          },\r\n          \"targentPrice\": {\r\n            \"nullable\": true,\r\n            \"oneOf\": [\r\n              {\r\n                \"$ref\": \"#/components/schemas/TransferProductPrcie\"\r\n              }\r\n            ]\r\n          }\r\n        }\r\n      },\r\n      \"TransferProductPrcie\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"price\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\",\r\n            \"nullable\": true\r\n          },\r\n          \"priceBase\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"CreateTransferRequestDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"description\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"currentStore\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"targetStore\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"currentWareHouse\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\"\r\n          },\r\n          \"targetWareHouse\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"guid\"\r\n          },\r\n          \"transferProductInfos\": {\r\n            \"type\": \"array\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/TransferProductInfo\"\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"UpdateStoreInventoryDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"storeId\",\r\n          \"wareHouseId\",\r\n          \"barcode\",\r\n          \"onHand\"\r\n        ],\r\n        \"properties\": {\r\n          \"storeId\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"userId\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"wareHouseId\": {\r\n            \"type\": \"string\",\r\n            \"minLength\": 1\r\n          },\r\n          \"barcode\": {\r\n            \"type\": \"string\",\r\n            \"minLength\": 1\r\n          },\r\n          \"onHand\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\"\r\n          }\r\n        }\r\n      },\r\n      \"DepartmentDifferenceReportDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"departmentName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"groupName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"groupNumber\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"unitsSoldFirst\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\"\r\n          },\r\n          \"unitsSoldLast\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\"\r\n          },\r\n          \"unitsSoldDifference\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\"\r\n          },\r\n          \"totalSaleFirst\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\"\r\n          },\r\n          \"totalSaleLast\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\"\r\n          },\r\n          \"totalSaleDifference\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\"\r\n          },\r\n          \"transactionsFirst\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"transactionsLast\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"transactionsDifference\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"transactionAvgFirst\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"double\",\r\n            \"nullable\": true\r\n          },\r\n          \"transactionAvgLast\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"double\",\r\n            \"nullable\": true\r\n          },\r\n          \"transactionAvgDifference\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"double\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"DepartmentBodys\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"firstBody\",\r\n          \"lastBody\"\r\n        ],\r\n        \"properties\": {\r\n          \"firstBody\": {\r\n            \"$ref\": \"#/components/schemas/DepartmentBody\"\r\n          },\r\n          \"lastBody\": {\r\n            \"$ref\": \"#/components/schemas/DepartmentBody\"\r\n          }\r\n        }\r\n      },\r\n      \"DepartmentBody\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"storeIds\",\r\n          \"beginDate\",\r\n          \"endDate\"\r\n        ],\r\n        \"properties\": {\r\n          \"storeIds\": {\r\n            \"type\": \"array\",\r\n            \"items\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            }\r\n          },\r\n          \"beginDate\": {\r\n            \"type\": \"string\",\r\n            \"minLength\": 1\r\n          },\r\n          \"endDate\": {\r\n            \"type\": \"string\",\r\n            \"minLength\": 1\r\n          }\r\n        }\r\n      },\r\n      \"ReportType\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"Store\",\r\n          \"Department\"\r\n        ],\r\n        \"enum\": [\r\n          \"Store\",\r\n          \"Department\"\r\n        ]\r\n      },\r\n      \"GroupDifferenceReportDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"firstTotalCount\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"lastTotalCount\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"totalDifference\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"groupsDifferences\": {\r\n            \"type\": \"array\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/GroupDifferenceDto\"\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"GroupDifferenceDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"groupName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"firstCount\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"lastCount\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"difference\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          }\r\n        }\r\n      },\r\n      \"StoreGroupComparisonDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"groupName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"groupNumber\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"storeName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"storeId\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"totalSale\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\"\r\n          },\r\n          \"unitsSold\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\"\r\n          },\r\n          \"transactions\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"transactionAvg\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"double\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"SalesRankingDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"barcode\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"description\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"qtySold\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\"\r\n          },\r\n          \"sales\": {\r\n            \"type\": \"number\",\r\n            \"format\": \"decimal\"\r\n          },\r\n          \"imageUrl\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"StoresDTO\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"stores\": {\r\n            \"type\": \"array\",\r\n            \"nullable\": true,\r\n            \"items\": {\r\n              \"$ref\": \"#/components/schemas/StoreDTO\"\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"StoreDTO\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"id\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"encryptedId\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"storename\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"startingHourOfTheDay\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"beginDayOfWeek\": {\r\n            \"$ref\": \"#/components/schemas/DayOfWeek\"\r\n          },\r\n          \"dataMinerAccessKey\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"companyId\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\",\r\n            \"nullable\": true\r\n          },\r\n          \"companyName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"storeSetting\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"connectionStatus\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"pointBalanceAccessKey\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"greetingMessage\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"logoLink\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"primaryColor\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"DayOfWeek\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"Sunday\",\r\n          \"Monday\",\r\n          \"Tuesday\",\r\n          \"Wednesday\",\r\n          \"Thursday\",\r\n          \"Friday\",\r\n          \"Saturday\"\r\n        ],\r\n        \"enum\": [\r\n          \"Sunday\",\r\n          \"Monday\",\r\n          \"Tuesday\",\r\n          \"Wednesday\",\r\n          \"Thursday\",\r\n          \"Friday\",\r\n          \"Saturday\"\r\n        ]\r\n      },\r\n      \"EventResponse\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"contentModel\"\r\n        ],\r\n        \"properties\": {\r\n          \"contentModel\": {\r\n            \"$ref\": \"#/components/schemas/EventItemModel\"\r\n          }\r\n        }\r\n      },\r\n      \"EventItemModel\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"required\": [\r\n          \"eventId\",\r\n          \"eventDesc\",\r\n          \"eventDate\",\r\n          \"approved\",\r\n          \"approvrdById\",\r\n          \"approvedByName\",\r\n          \"jrCashierId\",\r\n          \"jrCashierName\",\r\n          \"neededManagerLevel\",\r\n          \"jrtrx\",\r\n          \"reg\"\r\n        ],\r\n        \"properties\": {\r\n          \"eventId\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"eventDesc\": {\r\n            \"type\": \"string\",\r\n            \"minLength\": 1\r\n          },\r\n          \"eventDate\": {\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\",\r\n            \"minLength\": 1\r\n          },\r\n          \"approved\": {\r\n            \"$ref\": \"#/components/schemas/EventApproved\"\r\n          },\r\n          \"approvrdById\": {\r\n            \"type\": \"string\",\r\n            \"minLength\": 1\r\n          },\r\n          \"approvedByName\": {\r\n            \"type\": \"string\",\r\n            \"minLength\": 1\r\n          },\r\n          \"jrCashierId\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"jrCashierName\": {\r\n            \"type\": \"string\",\r\n            \"minLength\": 1\r\n          },\r\n          \"neededManagerLevel\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"jrtrx\": {\r\n            \"type\": \"string\",\r\n            \"minLength\": 1\r\n          },\r\n          \"reg\": {\r\n            \"type\": \"string\",\r\n            \"minLength\": 1\r\n          }\r\n        }\r\n      },\r\n      \"EventApproved\": {\r\n        \"type\": \"string\",\r\n        \"description\": \"\",\r\n        \"x-enumNames\": [\r\n          \"Awaiting_Authorization\",\r\n          \"Authorized\",\r\n          \"Deny_Authorization\",\r\n          \"Cancel_Authorization\"\r\n        ],\r\n        \"enum\": [\r\n          \"Awaiting_Authorization\",\r\n          \"Authorized\",\r\n          \"Deny_Authorization\",\r\n          \"Cancel_Authorization\"\r\n        ]\r\n      },\r\n      \"UsersDto\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"id\": {\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          \"userName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"companyName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"stores\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      },\r\n      \"UpdateUser\": {\r\n        \"type\": \"object\",\r\n        \"additionalProperties\": false,\r\n        \"properties\": {\r\n          \"companyName\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          },\r\n          \"stores\": {\r\n            \"type\": \"string\",\r\n            \"nullable\": true\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"securitySchemes\": {\r\n      \"Bearer\": {\r\n        \"type\": \"apiKey\",\r\n        \"description\": \"Authorization: Bearer {token}\",\r\n        \"name\": \"Authorization\",\r\n        \"in\": \"header\",\r\n        \"scheme\": \"Bearer\",\r\n        \"bearerFormat\": \"JWT\"\r\n      }\r\n    }\r\n  }\r\n}", "url": "https://**********:44373/swagger/v1/swagger.json", "output": null, "newLineBehavior": "Auto"}}, "codeGenerators": {"openApiToTypeScriptClient": {"className": "{controller}Client", "moduleName": "", "namespace": "", "typeScriptVersion": 4.3, "template": "Angular", "promiseType": "Promise", "httpClass": "HttpClient", "withCredentials": false, "useSingletonProvider": false, "injectionTokenType": "InjectionToken", "rxJsVersion": 7.0, "dateTimeType": "Date", "nullValue": "Undefined", "generateClientClasses": true, "generateClientInterfaces": false, "generateOptionalParameters": true, "exportTypes": true, "wrapDtoExceptions": false, "exceptionClass": "ApiException", "clientBaseClass": null, "wrapResponses": true, "wrapResponseMethods": [], "generateResponseClasses": true, "responseClass": "SwaggerResponse", "protectedMethods": [], "configurationClass": null, "useTransformOptionsMethod": false, "useTransformResultMethod": false, "generateDtoTypes": true, "operationGenerationMode": "MultipleClientsFromOperationId", "markOptionalProperties": true, "generateCloneMethod": false, "typeStyle": "Class", "enumStyle": "Enum", "useLeafType": false, "classTypes": [], "extendedClasses": [], "extensionCode": null, "generateDefaultValues": true, "excludedTypeNames": [], "excludedParameterNames": [], "handleReferences": false, "generateTypeCheckFunctions": false, "generateConstructorInterface": true, "convertConstructorInterfaceData": false, "importRequiredTypes": true, "useGetBaseUrlMethod": false, "baseUrlTokenName": "API_BASE_URL", "queryNullValue": "", "useAbortSignal": false, "inlineNamedDictionaries": false, "inlineNamedAny": false, "includeHttpContext": false, "templateDirectory": null, "serviceHost": null, "serviceSchemes": null, "output": "app/service/backoffice.ts", "newLineBehavior": "Auto"}}}