// 现代化产品配置弹窗样式
:host {
  // 现代化配色系统
  --primary-color: #2563eb;
  --primary-light: #3b82f6;
  --primary-dark: #1d4ed8;
  --secondary-color: #64748b;
  --accent-color: #06b6d4;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;

  // 中性色调
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  // 现代化阴影系统
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  // 现代化渐变
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);

  // 动画时长
  --transition-fast: 0.15s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;

  // 圆角系统
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 20px;
}

:host ::ng-deep .food-detail-dialog-container {
  .mat-mdc-dialog-container {
    padding: 0 !important;
    border-radius: var(--radius-2xl) !important;
    overflow: hidden !important;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%) !important;
    box-shadow: var(--shadow-2xl) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    backdrop-filter: blur(10px) !important;
  }

  .mat-mdc-dialog-surface {
    border-radius: var(--radius-2xl) !important;
    background: transparent !important;
  }

  .mat-mdc-dialog-content {
    margin: 0 !important;
    padding: 0 !important;
    max-height: none !important;
  }

  .mat-mdc-dialog-actions {
    margin: 0 !important;
    padding: 0 !important;
    min-height: auto !important;
  }
}

.food-detail-dialog {
  max-width: 1300px;
  width: 95vw;
  max-height: 117vh;
  background: transparent;
  border-radius: var(--radius-2xl);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;

  // 添加微妙的背景纹理
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      linear-gradient(145deg, #ffffff 0%, #f8fafc 100%),
      radial-gradient(circle at 20% 80%, rgba(37, 99, 235, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
  }

  .dialog-header {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 10;

    .close-button {
      // 简约大气的背景
      background: rgba(255, 255, 255, 0.9);
      color: #64748b;
      backdrop-filter: blur(8px);
      border-radius: 50%;
      width: 44px;
      height: 44px;
      border: 1px solid rgba(226, 232, 240, 0.6);

      // 简洁的阴影
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

      // 平滑过渡
      transition: all 0.2s ease;

      // 确保图标居中
      display: flex;
      align-items: center;
      justify-content: center;

      // 彻底移除Angular Material默认样式
      &.mat-mdc-icon-button,
      &.mat-icon-button {
        --mdc-icon-button-state-layer-size: 44px;
        --mdc-icon-button-icon-size: 20px;

        // 移除所有Material Design效果
        .mat-mdc-button-persistent-ripple,
        .mat-mdc-button-ripple,
        .mat-ripple,
        .mat-button-ripple {
          display: none !important;
        }

        // 移除所有焦点指示器
        .mat-mdc-focus-indicator,
        .mat-focus-indicator {
          display: none !important;
        }

        // 移除状态层
        .mdc-button__ripple,
        .mat-mdc-button-touch-target {
          display: none !important;
        }

        // 确保没有默认的焦点样式
        &:focus,
        &.cdk-focused {
          background: rgba(255, 255, 255, 0.9) !important;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
        }
      }

      // 图标样式
      mat-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      &:hover {
        background: rgba(255, 255, 255, 0.95);
        color: #374151;
        transform: translateY(-1px);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
        border-color: rgba(226, 232, 240, 0.8);
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        background: rgba(248, 250, 252, 0.9);
      }

      // 彻底移除所有焦点样式
      &:focus,
      &:focus-visible,
      &:focus-within,
      &.cdk-focused,
      &.mat-mdc-button-base.cdk-focused {
        outline: none !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
        background: rgba(255, 255, 255, 0.9) !important;
        border-color: rgba(226, 232, 240, 0.6) !important;
      }

      // 确保鼠标点击和初始状态不显示任何焦点效果
      &:focus:not(:focus-visible) {
        outline: none !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
        background: rgba(255, 255, 255, 0.9) !important;
        border-color: rgba(226, 232, 240, 0.6) !important;
      }
    }
  }

  .dialog-content {
    padding: 0;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    position: relative;
    z-index: 1;

    // 左侧：图片、价格、详细信息
    .left-section {
      flex: 0 0 57.7%;
      padding: 24px;
      background: linear-gradient(145deg, #f8fafc 0%, rgba(248, 250, 252, 0.8) 100%);
      border-right: 1px solid rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);
      display: flex;
      flex-direction: column;
      overflow-y: auto;

      .image-carousel-container {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;

        .image-carousel {
          position: relative;
          width: 100%;
          max-width: 650px;
          height: 364px;
          border-radius: 16px;
          overflow: hidden;
          background: #ffffff;
          box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
          touch-action: pan-x; // 允许水平滑动
          cursor: grab;

          // 鼠标悬停时显示导航按钮
          &:hover .navigation-buttons {
            opacity: 1;
            transform: scale(1);
          }

          .image-container {
            display: flex;
            width: 100%;
            height: 100%;
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);

            .image-slide {
              flex: 0 0 100%;
              width: 100%;
              height: 100%;
              position: relative;

              .product-image {
                width: 100%;
                height: 100%;
                object-fit: cover;
                user-select: none;
                -webkit-user-drag: none;
                background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
                transition: opacity 0.3s ease-in-out;

                &[src=""], &:not([src]) {
                  opacity: 0;
                }

                &.loaded {
                  opacity: 1;
                }
              }
            }
          }

          // 导航按钮容器
          .navigation-buttons {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            opacity: 0;
            transform: scale(0.9);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            pointer-events: none;
            z-index: 3;

            .nav-button {
              width: 52px;
              height: 52px;
              border-radius: 50%;
              border: 1px solid rgba(255, 255, 255, 0.2);
              background:
                linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%),
                radial-gradient(circle at 30% 30%, rgba(59, 130, 246, 0.1) 0%, transparent 50%);
              backdrop-filter: blur(12px);
              -webkit-backdrop-filter: blur(12px);
              color: #1f2937;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
              box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.12),
                0 2px 8px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
              pointer-events: auto;
              position: relative;
              overflow: hidden;

              // 内部光晕效果
              &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                border-radius: 50%;
                background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, transparent 50%);
                opacity: 0;
                transition: opacity 0.3s ease;
              }

              &:hover {
                background:
                  linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(255, 255, 255, 0.92) 100%),
                  radial-gradient(circle at 30% 30%, rgba(59, 130, 246, 0.15) 0%, transparent 50%);
                transform: scale(1.08) translateY(-1px);
                box-shadow:
                  0 12px 40px rgba(0, 0, 0, 0.15),
                  0 4px 12px rgba(0, 0, 0, 0.1),
                  inset 0 1px 0 rgba(255, 255, 255, 0.5);
                border-color: rgba(255, 255, 255, 0.3);

                &::before {
                  opacity: 1;
                }
              }

              &:active {
                transform: scale(0.96) translateY(0);
                transition: all 0.1s ease;
              }

              &:disabled {
                opacity: 0.4;
                cursor: not-allowed;
                transform: none;
                background: rgba(255, 255, 255, 0.6);
                backdrop-filter: blur(8px);
              }

              mat-icon {
                font-size: 26px;
                width: 26px;
                height: 26px;
                font-weight: 500;
                z-index: 1;
                position: relative;
              }
            }
          }

          .pagination-indicator {
            position: absolute;
            bottom: 16px;
            right: 16px;
            background:
              linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.6) 100%);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            color: white;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 0.75rem;
            font-weight: 600;
            pointer-events: none;
            z-index: 2;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
          }
        }
      }

      // 左侧的商品信息样式
      .product-header {
        margin-bottom: 20px;
        padding-bottom: 16px;

        .price-display {
          .current-price {
            font-size: 1.5rem;
            font-weight: 800;
            color: #2563eb;
            position: relative;

            &::after {
              content: '';
              position: absolute;
              bottom: -2px;
              left: 0;
              right: 0;
              height: 2px;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              border-radius: 1px;
              opacity: 0.3;
            }
          }
        }
      }

      .product-details-section {
        margin-bottom: 20px;
        padding-bottom: 16px;

        h3 {
          font-size: 1.125rem;
          font-weight: 600;
          color: #334155;
          margin: 0 0 12px 0;
        }

        .product-description {
          color: #64748b;
          line-height: 1.6;
          margin: 0;
        }
      }

      .loading-section, .error-section {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        padding: 20px;
        text-align: center;
        color: #64748b;
      }
    }

    // 右侧：规格选择区域
    .right-section {
      flex: 0 0 42.3%;
      padding: 24px 28px;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      position: relative;
      height: 780px; // 固定高度，彻底避免大小变化
      overflow: hidden; // 防止整体滚动

      // 添加微妙的内容渐变
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent 0%, rgba(37, 99, 235, 0.2) 50%, transparent 100%);
      }

      // 内容容器 - 绝对定位实现无缝切换
      .content-container {
        position: relative;
        width: 100%;
        height: 100%;

        // 所有内容区域使用绝对定位重叠
        .skeleton-section,
        .options-section,
        .error-section {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0; // 添加bottom确保填满容器
          opacity: 0;
          pointer-events: none;
          transition: none; // 移除所有过渡效果
          overflow-y: auto; // 允许内容滚动
          padding-right: 8px; // 为滚动条预留空间

          &.visible {
            opacity: 1;
            pointer-events: auto;
          }
        }
      }

      // 商品详情描述区域
      .product-details-section {
        margin-bottom: 24px;
        padding-bottom: 16px;
        border-bottom: 1px solid #e2e8f0;
        min-height: 80px; // 确保固定高度，保持布局一致性

        h3 {
          font-size: 1.125rem;
          font-weight: 600;
          color: #1e293b;
          margin: 0 0 12px 0;
        }

        .product-description {
          font-size: 0.875rem;
          line-height: 1.6;
          color: #64748b;
          margin: 0;
          white-space: pre-wrap; // 保持换行格式
          min-height: 40px; // 确保即使没有内容也有最小高度

          // 空内容时的样式
          &:empty {
            display: block; // 确保空元素仍占用空间
          }
        }
      }

      .loading-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%; // 填满固定容器
        padding: 32px 0;

        .loading-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 16px;
        }

        mat-spinner {
          ::ng-deep circle {
            stroke: #3b82f6;
          }
        }

        p {
          margin: 0;
          color: #64748b;
          font-size: 0.875rem;
          font-weight: 500;
        }
      }

      .error-section {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        gap: 12px;
        padding: 16px;
        background: #fef2f2;
        border-radius: 8px;
        color: #dc2626;
        border: 1px solid #fecaca;
        height: 100%; // 填满固定容器

        mat-icon {
          font-size: 20px;
          color: #dc2626;
        }

        p {
          margin: 0;
          font-size: 0.875rem;
          font-weight: 500;
        }
      }

      // 骨架屏区域
      .skeleton-section {
        margin-bottom: 28px;
        // 移除动画，实现瞬间切换

        .skeleton-option-group {
          // 精确匹配实际选项组高度：144px
          height: 144px;
          margin-bottom: 0; // 移除间距，由固定高度控制
          padding: 0;
          display: flex;
          flex-direction: column;

          .skeleton-group-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 32px; // 精确匹配实际标题高度
            margin-bottom: 16px;

            .skeleton-title {
              height: 20px;
              width: 140px;
              background: linear-gradient(90deg, #f1f5f9 25%, #e2e8f0 50%, #f1f5f9 75%);
              background-size: 200% 100%;
              animation: shimmer 1.5s infinite;
              border-radius: 4px;
            }

            .skeleton-badge {
              height: 16px;
              width: 60px;
              background: linear-gradient(90deg, #fef2f2 25%, #fecaca 50%, #fef2f2 75%);
              background-size: 200% 100%;
              animation: shimmer 1.5s infinite;
              border-radius: 8px;
            }
          }

          .skeleton-select-field {
            height: 56px; // 精确匹配mat-form-field高度
            width: 100%;
            background: linear-gradient(90deg, #f8fafc 25%, #f1f5f9 50%, #f8fafc 75%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
            border-radius: 8px;
            margin-bottom: 16px;
          }

          // 选中标签预留空间
          .skeleton-selected-tags {
            height: 40px; // 为选中标签预留空间
            display: flex;
            gap: 12px;
            align-items: center;

            .skeleton-tag {
              height: 32px;
              width: 80px;
              background: linear-gradient(90deg, #f0fdf4 25%, #dcfce7 50%, #f0fdf4 75%);
              background-size: 200% 100%;
              animation: shimmer 1.5s infinite;
              border-radius: 12px;
            }
          }
        }


      }

      // 选项区域
      .options-section {
        padding-bottom: 28px; // 底部留白，改为padding避免影响滚动
        // 移除动画，实现瞬间切换

        .option-group {
          // 恢复正常的间距，让内容可以自然滚动
          margin-bottom: 24px;

          .required-group {
            padding: 0;
            margin-bottom: 16px; // 恢复正常间距
            margin-right: 60px; // 为关闭按钮预留空间

            .group-header {
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-bottom: 16px;

              h3 {
                font-size: 1.125rem;
                font-weight: 600;
                color: #0f172a;
                margin: 0;
                letter-spacing: -0.025em;
              }

              .required-badge {
                color: #ef4444;
                font-size: 0.75rem;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.025em;
              }
            }

            // 下拉选择组样式
            .select-group {
              margin-bottom: 14px; // 缩小选择框间距

              .full-width {
                width: 100%;
              }

              ::ng-deep {
                .mat-mdc-form-field {
                  .mat-mdc-text-field-wrapper {
                    background: rgba(255, 255, 255, 0.8);
                    border-radius: var(--radius-md);
                    transition: all var(--transition-normal) ease;

                    &:hover {
                      background: rgba(255, 255, 255, 0.95);
                      box-shadow: var(--shadow-sm);
                    }
                  }

                  &.mat-focused .mat-mdc-text-field-wrapper {
                    background: rgba(255, 255, 255, 1);
                    box-shadow: var(--shadow-md);
                  }
                }

                .mat-mdc-option {
                  transition: all var(--transition-fast) ease;

                  &:hover {
                    background: linear-gradient(135deg, rgba(37, 99, 235, 0.05) 0%, rgba(6, 182, 212, 0.05) 100%);
                  }

                  .option-name {
                    font-size: 0.875rem;
                    color: var(--gray-700);
                    font-weight: 500;
                  }

                  .option-price {
                    font-size: 0.875rem;
                    color: var(--primary-color);
                    font-weight: 600;
                    margin-left: 8px;
                  }
                }
              }
            }

            .radio-group, .checkbox-group {
              .option-item {
                margin-bottom: 12px;

                .option-radio, .option-checkbox {
                  width: 100%;

                  .option-content {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    width: 100%;

                    .option-name {
                      font-size: 0.875rem;
                      color: #374151;
                    }

                    .option-price {
                      font-size: 0.875rem;
                      font-weight: 600;
                      color: #2563eb;
                    }
                  }
                }
              }
            }
          }

          .optional-group {
            margin-bottom: 16px; // 恢复正常间距
            padding: 0;
            margin-right: 60px; // 为关闭按钮预留空间

            .group-header {
              margin-bottom: 16px;

              h3 {
                font-size: 1.125rem;
                font-weight: 600;
                color: #0f172a;
                margin: 0;
                letter-spacing: -0.025em;
              }
            }

            // 下拉选择组样式
            .select-group {
              margin-bottom: 14px; // 缩小选择框间距

              .full-width {
                width: 100%;
              }

              ::ng-deep {
                .mat-mdc-form-field {
                  .mat-mdc-text-field-wrapper {
                    background: rgba(255, 255, 255, 0.9);
                    border-radius: var(--radius-md);
                    transition: all var(--transition-normal) ease;

                    &:hover {
                      background: rgba(255, 255, 255, 1);
                      box-shadow: var(--shadow-sm);
                    }
                  }
                }

                .mat-mdc-option {
                  transition: all var(--transition-fast) ease;

                  &:hover {
                    background: linear-gradient(135deg, rgba(37, 99, 235, 0.05) 0%, rgba(6, 182, 212, 0.05) 100%);
                  }

                  .option-name {
                    font-size: 0.875rem;
                    color: var(--gray-700);
                    font-weight: 500;
                  }

                  .option-price {
                    font-size: 0.875rem;
                    color: var(--primary-color);
                    font-weight: 600;
                    margin-left: 8px;
                  }
                }
              }
            }

            // 选中项目标签样式
            .selected-items {
              display: flex;
              flex-wrap: wrap;
              gap: 12px;
              margin-top: 16px;

              .selected-item-tag {
                display: flex;
                align-items: center;
                background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(6, 182, 212, 0.1) 100%);
                border: 1px solid rgba(16, 185, 129, 0.3);
                border-radius: var(--radius-lg);
                padding: 8px 14px;
                font-size: 0.875rem;
                transition: all var(--transition-normal) ease;
                box-shadow: var(--shadow-sm);
                animation: tagAppear var(--transition-normal) ease-out;

                &:hover {
                  transform: translateY(-1px);
                  box-shadow: var(--shadow-md);
                  border-color: rgba(16, 185, 129, 0.5);
                  background: linear-gradient(135deg, rgba(16, 185, 129, 0.15) 0%, rgba(6, 182, 212, 0.15) 100%);
                }

                .check-icon {
                  width: 18px;
                  height: 18px;
                  font-size: 18px;
                  color: var(--success-color);
                  margin-right: 8px;
                  animation: checkPulse 2s infinite;
                }

                .item-name {
                  color: var(--gray-700);
                  font-weight: 600;
                  letter-spacing: -0.025em;
                }

                .item-price {
                  color: var(--primary-color);
                  font-weight: 700;
                  margin-left: 8px;
                  font-size: 0.8125rem;
                }
              }
            }
          }
        }
      }



      // 验证错误
      .validation-errors {
        margin-bottom: 16px;

        .error-message {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 12px;
          background: #fef2f2;
          border: 1px solid #fecaca;
          border-radius: 6px;
          margin-bottom: 8px;

          mat-icon {
            font-size: 16px;
          }

          span {
            font-size: 0.875rem;
            color: #dc2626;
          }
        }
      }
    }
  }

  // 规格选择和备注区域的父容器
  .options-and-memo-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  // Notes section styles - Aligned with specification selection boxes
  .memo-section {
    padding: 0;
    margin-right: 60px; // Consistent right margin with specification selection boxes
    margin-bottom: 16px; // Add bottom margin for better spacing
    margin-top: 24px; // Ensure adequate top margin to prevent label overlap
    
    // Add entrance animation
    animation: slideInUp var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
    animation-fill-mode: both;
    animation-delay: 0.3s; // Appear slightly later than option groups
    
    // Initial state (before animation)
    opacity: 0;
    transform: translateY(20px);

    .memo-field {
      width: 100%; // Maintain consistent width with specification selection boxes
      min-height: 120px; // Ensure minimum height for consistency
      
      ::ng-deep {
        .mat-mdc-form-field {
          width: 100%;
          // Ensure adequate padding for floating label
          padding-top: 16px;
          
          .mat-mdc-text-field-wrapper {
            background: rgba(255, 255, 255, 0.8);
            border-radius: var(--radius-md);
            transition: all var(--transition-normal) ease;
            min-height: 80px; // Ensure consistent minimum height

            &:hover {
              background: rgba(255, 255, 255, 0.95);
              box-shadow: var(--shadow-sm);
            }
          }

          &.mat-focused .mat-mdc-text-field-wrapper {
            background: rgba(255, 255, 255, 1);
            box-shadow: var(--shadow-md);
          }

          .mat-mdc-floating-label {
            color: #6b7280;
            font-weight: 500;
            font-size: 0.875rem;
            // Ensure label has enough space and won't be cut off
            z-index: 10;
            position: relative;

            &.mdc-floating-label--float-above {
              color: #3b82f6;
              font-size: 0.75rem;
              // Ensure floating label is positioned correctly
              transform: translateY(-24px) scale(0.75) !important;
            }
          }

          // Ensure the form field has adequate top space for floating label
          .mat-mdc-form-field-flex {
            padding-top: 20px;
          }

          .mat-mdc-form-field-hint {
            color: #9ca3af;
            font-size: 0.75rem;
            font-weight: 400;
            margin-top: 4px;
          }
        }

        textarea.mat-mdc-input-element,
        .memo-textarea {
          resize: vertical;
          min-height: 60px;
          max-height: 200px; // Prevent textarea from growing too large
          font-size: 0.875rem;
          line-height: 1.5;
          color: #374151;
          padding: 12px 14px;
          border: none;
          outline: none;
          background: transparent;
          width: 100%;
          box-sizing: border-box;
          
          // Better text wrapping and overflow handling
          word-wrap: break-word;
          word-break: break-word;
          overflow-wrap: break-word;
          white-space: pre-wrap;
          
          // Smooth scrolling for long content
          overflow-y: auto;
          scrollbar-width: thin;
          scrollbar-color: #cbd5e1 transparent;
          
          // Webkit scrollbar styling
          &::-webkit-scrollbar {
            width: 6px;
          }
          
          &::-webkit-scrollbar-track {
            background: transparent;
          }
          
          &::-webkit-scrollbar-thumb {
            background-color: #cbd5e1;
            border-radius: 3px;
            
            &:hover {
              background-color: #94a3b8;
            }
          }

          &::placeholder {
            color: #9ca3af;
            font-style: italic;
            opacity: 0.8;
          }
          
          &:focus {
            &::placeholder {
              opacity: 0.6;
            }
          }
        }
      }
    }
  }



  .dialog-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    padding: 24px 28px !important;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
    border-top: 1px solid rgba(226, 232, 240, 0.6);
    flex-shrink: 0;
    backdrop-filter: blur(10px);
    position: relative;
    z-index: 100;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent 0%, rgba(37, 99, 235, 0.3) 50%, transparent 100%);
    }

    .quantity-controls {
      display: flex;
      align-items: center;
      gap: 16px;
      background: linear-gradient(145deg, rgba(248, 250, 252, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%);
      border: 1px solid rgba(226, 232, 240, 0.6);
      border-radius: var(--radius-lg);
      padding: 8px 12px;
      box-shadow: var(--shadow-sm);

      .quantity-btn {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
        border: 1px solid rgba(226, 232, 240, 0.5);
        color: #475569;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        font-size: 18px;
        font-weight: 500;
        cursor: pointer;

        &:hover:not(:disabled) {
          background: linear-gradient(145deg, #f8fafc 0%, #e2e8f0 100%);
          color: #1e293b;
          transform: scale(1.05);
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        &:active:not(:disabled) {
          transform: scale(0.95);
        }

        &:disabled {
          opacity: 0.4;
          cursor: not-allowed;
          transform: none;
        }
      }

      .quantity-display {
        font-size: 1.125rem;
        font-weight: 700;
        color: #0f172a;
        min-width: 32px;
        text-align: center;
        letter-spacing: -0.025em;
      }
    }

    .add-to-cart-btn {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      gap: 8px !important;
      padding: 12px 20px !important;
      background: #2563eb !important;
      color: white !important;
      border: none !important;
      border-radius: 8px !important;
      font-weight: 600 !important;
      font-size: 0.9375rem !important;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
      letter-spacing: -0.025em !important;
      min-width: auto !important;
      flex-shrink: 0 !important;
      width: auto !important;
      flex: none !important;
      position: relative !important;
      z-index: 101 !important;

      &:hover:not(:disabled) {
        background: #1d4ed8 !important;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
        transform: translateY(-1px) !important;
      }

      &:active:not(:disabled) {
        transform: translateY(0) !important;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
        animation: buttonPress 0.15s ease-out !important;
      }

      &:disabled {
        opacity: 0.6 !important;
        cursor: not-allowed !important;
        transform: none !important;
        background: #94a3b8 !important;
      }
    }
  }
}

// 现代化动画效果
@keyframes checkPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

// 关闭按钮旋转动画
@keyframes closeButtonRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(90deg);
  }
}

// 关闭按钮脉冲效果
@keyframes closeButtonPulse {
  0% {
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.12),
      0 4px 16px rgba(0, 0, 0, 0.08),
      0 2px 8px rgba(0, 0, 0, 0.04),
      inset 0 1px 0 rgba(255, 255, 255, 0.6),
      inset 0 -1px 0 rgba(0, 0, 0, 0.05);
  }
  50% {
    box-shadow:
      0 12px 40px rgba(0, 0, 0, 0.15),
      0 6px 20px rgba(0, 0, 0, 0.1),
      0 3px 12px rgba(0, 0, 0, 0.06),
      inset 0 1px 0 rgba(255, 255, 255, 0.8),
      inset 0 -1px 0 rgba(0, 0, 0, 0.03);
  }
  100% {
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.12),
      0 4px 16px rgba(0, 0, 0, 0.08),
      0 2px 8px rgba(0, 0, 0, 0.04),
      inset 0 1px 0 rgba(255, 255, 255, 0.6),
      inset 0 -1px 0 rgba(0, 0, 0, 0.05);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes buttonPress {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.98);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes tagAppear {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// 为弹窗添加入场动画
.food-detail-dialog {
  animation: fadeInScale var(--transition-slow) cubic-bezier(0.4, 0, 0.2, 1);
}

// 为选项组添加入场动画
.option-group {
  animation: slideInUp var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
  animation-fill-mode: both;

  &:nth-child(1) { animation-delay: 0.1s; }
  &:nth-child(2) { animation-delay: 0.2s; }
  &:nth-child(3) { animation-delay: 0.3s; }
  &:nth-child(4) { animation-delay: 0.4s; }
}

// 现代化响应式设计
@media (max-width: 768px) {
  .food-detail-dialog {
    max-width: 95vw;
    max-height: 95vh;
    margin: 8px;

    .dialog-header .close-button {
      width: 40px;
      height: 40px;
      top: 16px;
      right: -8px; // 增加右边距，避免挡住内容

      // 移动端简约阴影
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);

      // 移动端图标尺寸调整
      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
      }
    }

    .dialog-content {
      flex-direction: column;
      overflow-y: auto; // 移动端整体滚动

      .left-section {
        flex: 0 0 auto;
        padding: 20px 16px;
        overflow-y: visible; // 移除独立滚动

        .image-carousel-container .image-carousel {
          height: 364px;
          max-width: 100%;

          // 移动端隐藏导航按钮，保留触摸滑动
          .navigation-buttons {
            display: none;
          }
        }
      }

      .right-section {
        flex: 0 0 auto; // 改为自适应，不再独立滚动
        padding: 20px 16px;
        overflow-y: visible; // 移除独立滚动

        .product-header {
          margin-bottom: 20px;
          padding-bottom: 16px;

          .product-title {
            font-size: 1.375rem;
          }

          .price-display .current-price {
            font-size: 1.375rem;
          }
        }

        .options-section {
          .option-group {
            margin-bottom: 10px; // 大幅缩小移动端真正的组间距

            .required-group, .optional-group {
              padding: 16px;
              margin-bottom: 0; // 内部间距设为0，由外层option-group控制
              margin-right: 50px; // 平板端为关闭按钮预留空间

              .group-header {
                margin-bottom: 12px; // 缩小移动端标题间距

                h3 {
                  font-size: 1rem;
                }
              }

              .select-group {
                margin-bottom: 12px; // 缩小移动端选择框间距
              }
            }
          }
        }

        // 平板端特殊说明区域右边距
        .instructions-section {
          margin-right: 50px;
        }
      }
    }

    .dialog-actions {
      padding: 20px 16px !important;
      gap: 16px;
      flex-wrap: wrap;

      .quantity-controls {
        gap: 12px;
        padding: 6px 10px;

        button {
          width: 32px;
          height: 32px;
        }

        .quantity-display {
          font-size: 1rem;
          min-width: 28px;
        }
      }

      .add-to-cart-btn {
        padding: 12px 18px !important;
        font-size: 0.9375rem !important;
        min-height: 44px !important;
        flex: 1;
        min-width: 200px;
      }
    }
  }
}

// 超小屏幕优化
@media (max-width: 480px) {
  .food-detail-dialog {
    max-width: 100vw;
    max-height: 100vh;
    margin: 0;
    border-radius: 0 !important;

    .dialog-content {
      .left-section {
        padding: 16px 12px;

        .image-carousel-container .image-carousel {
          height: 312px;
        }
      }

      .right-section {
        padding: 16px 12px;

        .product-header .product-title {
          font-size: 1.25rem;
        }

        .options-section .option-group {
          margin-bottom: 8px; // 大幅缩小超小屏幕真正的组间距

          .required-group, .optional-group {
            padding: 12px;
            margin-bottom: 0; // 内部间距设为0，由外层option-group控制
            margin-right: 45px; // 手机端为关闭按钮预留空间

            .group-header {
              margin-bottom: 10px; // 超小屏幕标题间距

              h3 {
                font-size: 0.9375rem;
              }
            }

            .select-group {
              margin-bottom: 10px; // 超小屏幕选择框间距
            }
          }
        }

        // 手机端特殊说明区域右边距
        .instructions-section {
          margin-right: 45px;
        }
      }
    }

    .dialog-actions {
      padding: 16px 12px !important;
      gap: 12px;

      .add-to-cart-btn {
        min-height: 48px !important;
      }
    }
  }
}

// 错误信息样式
.error-message {
  background-color: #fee2e2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 12px 16px;
  border-radius: 6px;
  margin: 16px;
  font-size: 0.875rem;
  text-align: center;
}

// 自定义触发器样式和箭头控制
::ng-deep .mat-mdc-select {
  // 当有选择时隐藏默认箭头
  &.has-selection {
    .mat-mdc-select-arrow-wrapper {
      display: none !important;
    }
  }

  // 没有选择时显示默认箭头，隐藏打勾图标
  &.no-selection {
    .mat-mdc-select-trigger .custom-trigger .trigger-check-icon {
      display: none !important;
    }
  }
}

::ng-deep .mat-mdc-select-trigger {
  .custom-trigger {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .trigger-text {
      flex: 1;
      color: var(--text-color);
      font-weight: 500;
    }

    .trigger-check-icon {
      width: 20px;
      height: 20px;
      font-size: 20px;
      color: var(--success-color);
      margin-left: 8px;
      flex-shrink: 0;
      // 确保打勾图标在箭头的位置
      margin-right: 4px;
    }
  }
}


