import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { CartService, CartState, CartItem } from '../../service/cart.service';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmDialogComponent } from '../confirm-dialog/confirm-dialog.component';
import { CheckoutConfirmationDialogComponent } from '../checkout-confirmation-dialog/checkout-confirmation-dialog.component';
import { FoodDetailDialogComponent } from '../food-detail-dialog/food-detail-dialog.component';
import { Food } from '../../data/food.data';
import { StorageService } from '../../service/storage.service';

@Component({
  selector: 'app-cart-sidebar',
  templateUrl: './cart-sidebar.component.html',
  styleUrls: ['./cart-sidebar.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatListModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    MatProgressBarModule
  ]
})
export class CartSidebarComponent implements OnInit, OnDestroy {
  isOpen = false;
  cartState: CartState | null = null;

  private cartStateSubscription?: Subscription;
  private sidebarSubscription?: Subscription;

  constructor(
    private cartService: CartService,
    private dialog: MatDialog,
    private router: Router,
    private storageService: StorageService
  ) {}

  ngOnInit(): void {
    // 订阅侧边栏开关状态
    this.sidebarSubscription = this.cartService.cartSidebarOpen$.subscribe(isOpen => {
      console.log('购物车侧边栏状态变化:', isOpen);
      this.isOpen = isOpen;
    });

    // 订阅购物车状态变化 - 使用新的本地状态
    this.cartStateSubscription = this.cartService.cartState$.subscribe(cartState => {
      console.log('购物车侧边栏收到状态更新:', cartState);
      this.cartState = cartState;
    });
  }

  ngOnDestroy(): void {
    if (this.cartStateSubscription) {
      this.cartStateSubscription.unsubscribe();
    }
    if (this.sidebarSubscription) {
      this.sidebarSubscription.unsubscribe();
    }
  }

  /**
   * 关闭侧边栏
   */
  closeSidebar(): void {
    console.log('TODO: 关闭购物车侧边栏');
    // TODO: 实现关闭侧边栏逻辑
  }

  /**
   * 增加商品数量
   */
  increaseQuantity(item: CartItem): void {
    console.log('增加商品数量:', item);

    this.cartService.increaseQuantity(item).subscribe({
      next: (response) => {
        console.log('=== 商品数量增加成功 ===');
        console.log('返回信息:', response);
      },
      error: (error) => {
        console.error('增加商品数量失败:', error);
      }
    });
  }

  /**
   * 减少商品数量或确认删除
   */
  decreaseQuantity(item: CartItem): void {
    console.log('减少商品数量:', item);

    if (item.quantity <= 1) {
      // 当数量为1时，弹出确认删除对话框
      this.confirmRemoveItem(item);
      return;
    }

    this.cartService.decreaseQuantity(item).subscribe({
      next: (response) => {
        console.log('=== 商品数量减少成功 ===');
        console.log('返回信息:', response);
      },
      error: (error) => {
        console.error('减少商品数量失败:', error);
      }
    });
  }

  /**
   * 确认删除商品对话框
   */
  private confirmRemoveItem(item: CartItem): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirm Removal',
        message: `Do you want to remove "${item.name}" from your cart?`,
        confirmText: 'Remove',
        cancelText: 'Cancel'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result === true) {
        this.removeItem(item);
      }
    });
  }

  /**
   * 删除商品
   */
  removeItem(item: CartItem): void {
    console.log('删除商品:', item);

    this.cartService.removeItem(item).subscribe({
      next: (response) => {
        console.log('=== 商品删除成功 ===');
        console.log('返回信息:', response);
      },
      error: (error) => {
        console.error('删除商品失败:', error);
      }
    });
  }

  /**
   * 获取选择的规格显示文本
   */
  getSelectedSpecs(selectedAttributes: any[]): string {
    if (!selectedAttributes || selectedAttributes.length === 0) {
      return '';
    }

    return selectedAttributes.map(attr => {
      if (attr.items && attr.items.length > 0) {
        return attr.items.map((item: any) => item.name).join(', ');
      }
      return '';
    }).filter(spec => spec).join('; ');
  }

  /**
   * 检查购物车是否为空
   */
  isCartEmpty(): boolean {
    return !this.cartState || this.cartState.items.length === 0;
  }

  /**
   * 编辑购物车商品
   */
  editItem(item: CartItem): void {
    console.log('编辑商品:', item.name);

    // 构造Food对象用于打开商品详情对话框
    // 由于后端会处理价格计算，我们只需要提供商品基本信息
    const food: Food = {
      id: item.foodItemId,
      name: item.name,
      description: item.description || '',
      price: 0, // 价格由后端计算，这里设为0
      image: item.image || '',
      categoryId: '', // 编辑时不需要分类ID
    };

    const dialogRef = this.dialog.open(FoodDetailDialogComponent, {
      maxWidth: '1300px',
      width: '95vw',
      maxHeight: '117vh',
      panelClass: 'food-detail-dialog-container',
      hasBackdrop: true,
      disableClose: false,
      data: {
        food: food,
        storeId: 2,
        editMode: true, // 标识为编辑模式
        cartItem: item // 传入购物车商品信息
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && result.action) {
        console.log('商品编辑对话框关闭:', result);
        // 这里可以添加编辑成功的反馈
      }
    });
  }

  /**
   * 获取购物车总价
   */
  getTotalAmount(): number {
    return this.cartState?.totalAmount || 0;
  }

  /**
   * 获取购物车税额
   */
  getTotalTaxAmount(): number {
    return this.cartState?.totalTaxAmount || 0;
  }

  /**
   * 获取购物车商品总数
   */
  getItemCount(): number {
    return this.cartState?.itemCount || 0;
  }

  /**
   * 检查加载状态
   */
  isSyncing(): boolean {
    return this.cartState?.loading === true;
  }

  /**
   * 检查是否有错误
   */
  hasError(): boolean {
    return this.cartState?.error !== null && this.cartState?.error !== undefined;
  }

  /**
   * 获取错误信息
   */
  getErrorMessage(): string {
    return this.cartState?.error || '';
  }

  /**
   * 清除错误信息
   */
  clearError(): void {
    this.cartService.clearError();
  }

  /**
   * 检查是否有同步错误
   */
  hasSyncError(): boolean {
    return false; // 简化版本不跟踪错误状态
  }

  /**
   * 前往结算 - 先显示确认弹窗
   */
  proceedToCheckout(): void {
    // 从本地存储获取storeId
    const storeIdStr = this.storageService.getStoreId();
    if (!storeIdStr) {
      console.error('未找到storeId，请先在管理员设置中配置');
      alert('未设置店铺ID，请联系管理员进行配置');
      return;
    }

    const storeId = parseInt(storeIdStr, 10);
    if (isNaN(storeId)) {
      console.error('storeId格式错误:', storeIdStr);
      alert('店铺ID格式错误，请联系管理员重新配置');
      return;
    }

    // 检查购物车状态
    if (!this.cartState || this.cartState.items.length === 0) {
      alert('购物车为空，无法结算');
      return;
    }

    console.log('打开结算确认弹窗:', { storeId, cartState: this.cartState });

    // 打开结算确认弹窗
    const dialogRef = this.dialog.open(CheckoutConfirmationDialogComponent, {
      width: '600px',
      maxWidth: '95vw',
      maxHeight: '90vh',
      disableClose: true,
      data: {
        cartState: this.cartState,
        storeId: storeId
      }
    });

    // 处理弹窗结果
    dialogRef.afterClosed().subscribe(result => {
      if (result && result.action === 'confirm') {
        console.log('用户确认结算，开始调用API:', result);
        this.performCheckout(result.storeId);
      } else {
        console.log('用户取消结算');
      }
    });
  }

  /**
   * 执行实际的结算操作
   */
  private performCheckout(storeId: number): void {
    console.log('开始执行结算:', storeId);

    this.cartService.checkout(storeId).subscribe({
      next: (orderResult) => {
        console.log('结算成功，跳转到结果页面:', orderResult);
        // 导航到结算结果页面，并传递订单结果
        this.router.navigate(['/checkout-result'], {
          state: { orderResult }
        });
      },
      error: (error) => {
        console.error('结算失败:', error);

        // 根据错误状态提供更友好的提示
        let message = '结算失败，请重试';
        if (error.status === 500) {
          message = '服务器暂时无法处理您的订单，请稍后重试或联系客服';
        } else if (error.status === 404) {
          message = '结算服务暂时不可用，请稍后重试';
        } else if (error.status === 400) {
          message = '订单信息有误，请刷新页面后重试';
        }

        alert(message);
      }
    });
  }



}
